\begin{document}
\section{Project Overview}

EdEngage is an interactive, multi-agent math tutoring platform designed for K–12 students and their teachers. Its goal is simple but ambitious: shift math assignments from being answer-focused tasks into step-by-step Socratic conversations—turning every problem into a learning opportunity that rewards thoughtful reasoning, not just speed or correctness. With this project, I hope to provide a useful resource to the students who are getting left behind in the  edtech revolution—especially the ones back of the classroom, those with learning differences, and those without easy access to private tutors.

My motivation for building EdEngage comes directly from my work as a tutor. For the past eight years, I’ve worked with students ranging from middle schoolers with learning disabilities to AP seniors—both one-on-one, through CHANCE Tutoring (a volunteer tutoring group I lead that provides tutoring to underserved students in Durham) and Family Tutoring Co. (a business I co-founded). Across hundreds of hours, I saw the same pattern: deep, lasting understanding didn’t come from right answers, but from guided dialogue—asking questions, not just giving solutions. Socratic questioning helped students overcome their misconceptions, and when the learning process was valued, students who struggled most started to believe they could succeed in math. EdEngage captures this approach in software. It’s also personal: my younger brother has a learning processing disorder and I found out recently that he had just been ChatGPTing all of his homework. It's heartbreaking to me, but it makes sense--its the path of least resistance for him. That’s why I want EdEngage to support—not replace—real learning, making tools that truly “get” their users.

At its core, EdEngage uses a carefully orchestrated team of AI agents:
\begin{itemize}
  \item \textbf{EDEN}, the Socratic tutor, doesn’t reveal answers but nudges students with questions, building metacognition and problem-solving step by step.
  \item \textbf{EVAL} checks every response for clarity, alignment with teaching goals, and whether EDEN slipped up and gave away answers, ensuring coachable, process-focused feedback.
  \item \textbf{ETHIC} classifies student responses—flagging attempts to shortcut, disengage, or abuse the system—and prompts for more effort or reflection as needed.
  \item \textbf{EDIFY} tracks each conversation, summarizing strengths, misconceptions, and progress for both the student and teacher.
  \item \textbf{ELICIT} adapts next questions to a student’s learning profile, readiness, and performance, offering both challenge and support.
  \item \textbf{ERGO} identifies which problem-solving step a student is tackling, scoring their understanding and ensuring fine-grained support.
  \item \textbf{EMBED} retrieves relevant teacher notes or learning resources when students request additional help.
\end{itemize}

EdEngage’s approach addresses a core incentive problem in math education: students are often rewarded for getting the answer quickly, not for showing their reasoning or persistence. This pushes them to seek shortcuts, whether it’s copying homework, googling solutions, or (now) pasting problems into ChatGPT. Most edtech tools—like Khan Academy—don’t require step-by-step thinking or explanations. Even newer AI tools rarely focus on how students think, only what answer they give. As a result, students who need support the most get the least insight, and teachers have little data on where students’ thinking breaks down. 

EdEngage was built to flip this script. When students work with the system, everything is structured as a dialogue. Concepts are broken into steps, and progress is made by asking and answering questions—never by copying answers or rushing to a result. If a student tries to shortcut or disengage, the ETHIC agent steps in and redirects them back to the process. All conversations are analyzed for effort, depth, and misconceptions. Teachers receive clear, actionable analytics—showing, for example, exactly where a student struggled, what strategies they used, and how their understanding changed over time. The system is built to be auditable and explainable: every AI decision can be reviewed, and analytics can be exported for teacher review or parent feedback. 

Equity is baked into EdEngage’s design. The system runs on standard school hardware, is accessible for students with language or learning differences, and prioritizes effort and progress over speed. Profiles and problems adapt to each student’s preferences, learning modality, and needs, based on an initial intake and ongoing data. Analytics disaggregate performance to help ensure students furthest from opportunity don’t fall through the cracks. When a student is stuck or losing confidence, the system introduces scaffolds, visual supports, or conceptual prompts—always focused on growth.

Finally, explainability and academic integrity are not afterthoughts. All agent responses are structured and reasoned—so teachers can see not only a student’s answer, but how they arrived there. If a suspected AI-generated or copy-paste response appears, it will be visible to the teacher in the backend. Data is stored securely and monitored for compliance, with privacy and fairness routinely audited. 

I've witnessed first hand how one-on-one tutoring sessions can boosted confidence among struggling students, but the real potential lies in building a system that puts the process—not just the product—of learning at the center, for every student, everywhere. As a tutor, I see EdEngage as a practical, auditable AI partner ready to help students and teachers work together—not around each other. That’s the future I care about building.

\section{Past Research}

EdEngage draws upon a robust set of theoretical foundations from recent research on intelligent tutoring systems (ITS), multi-agent pedagogies, Socratic dialogue, formative assessment, and equity-driven personalization. Key findings from these domains underscore the urgency and necessity of EdEngage’s approach.

\subsection*{Intelligent Tutoring Systems}

Research consistently demonstrates the significant impact of one-to-one tutoring and ITS. Bloom’s landmark ``2 Sigma Problem'' revealed that personalized tutoring could yield learning gains as high as two standard deviations compared to conventional classrooms~\cite{bloom1984sigma}. Recent meta-analyses confirm that modern ITS platforms now regularly approach these levels, averaging effect sizes around \( d = 0.76 \)~\cite{vanlehn2011relative}. EdEngage explicitly integrates these proven tutoring principles—adaptive feedback, personalized hints, and tailored scaffolding—to approach these optimal gains at scale.

\begin{table}[h]
\centering
\caption{Effect Sizes: Tutoring Approaches vs. Traditional Instruction}
\begin{tabular}{llc}
\toprule
\textbf{Approach} & \textbf{Effect Size (Cohen's \( d \))} & \textbf{Source} \\
\midrule
Mastery Learning (class-wide) & 1.00 & Bloom (1984) \\
One-to-one Human Tutoring (ideal) & 2.00 & Bloom (1984) \\
Intelligent Tutoring Systems (avg.) & 0.76 & VanLehn (2011) \\
\bottomrule
\end{tabular}
\end{table}

\subsection*{Dialogue Based Approach}

EdEngage's approach is supported by findings that specialized educational agents collaborating in dialogue significantly improve student engagement and cognitive outcomes. D'Mello et al.~\cite{dmello2014confusion} showed that agent-to-agent dialogues, especially when presenting conflicting approaches, induce productive confusion, driving deeper student reflection and conceptual learning. By consistently asking the student to explain their reasoning or justify their thoughts when they are off base, EDEN promotes curiosity and exploration. Similarly, systems that assign distinct educational roles—such as a content agent and an affective agent—enhance student motivation and lower anxiety, particularly for struggling learners~\cite{johnson2016multirole}.

\subsection*{Socratic Instruction}

Socratic methods, characterized by guided questioning rather than direct instruction, consistently produce deeper understanding and improved reasoning skills. Le’s (2019) analysis highlighted that Socratic questioning significantly improves students' abilities to justify and articulate their reasoning processes~\cite{le2019socratic}. Furthermore, dialogic instructional techniques have shown robust effects on conceptual comprehension (average effect size \( d \approx 0.35 \)) and increased student persistence and engagement in learning~\cite{chi2014icap}.

\subsection*{Process-Based Formative Assessment}

Research strongly supports process-oriented formative assessment—the evaluation of intermediate steps, reasoning, and strategies—as essential to mathematics learning. Hattie’s meta-analysis identifies specific and timely formative feedback as one of education’s strongest levers (\( d \approx 0.70 \))~\cite{hattie2012visible}. EdEngage’s stepwise evaluation structure directly implements this proven feedback methodology, allowing precise and immediate intervention at points of misconception. EDEN is also grounded in the current mathematical step, which ensures not only accuracy, but also a forced fixation on the sequential building blocks of the problem solving process.

\subsection*{Effort Detection and Behavioral Analytics}

Effective tutoring systems must monitor and address student effort and engagement. Baker et al.’s work on log-based effort metrics demonstrated high accuracy (often exceeding 80\%) in detecting student disengagement and gaming behaviors from interaction data \\alone~\cite{baker2008engagement}. EdEngage leverages these analytics via its ETHIC agent, proactively responding to low-effort behaviors and promoting productive engagement patterns.

\subsection*{Equity-Aware Personalization}

Adaptive personalization, when implemented with an equity lens, can effectively reduce achievement gaps. According to UNICEF (2022), personalized learning technologies show significant potential for closing educational disparities, particularly for lower-performing students~\cite{unicef2022adaptive}. EdEngage explicitly integrates equity considerations into its adaptation algorithms and analytics, ensuring that all students—particularly those historically underserved—benefit equitably from personalized instruction.

\subsection*{Recent Declines in Mathematics Achievement}

Recent data from national and international assessments underscore the urgent need for effective math interventions. NAEP (2022) reported historic declines in math scores—the largest in decades—with particularly stark drops among lower-performing students, exacerbating existing equity gaps~\cite{naep2022report}. Globally, OECD's PISA results similarly recorded unprecedented declines, underscoring the worldwide nature of the math achievement crisis~\cite{oecd2022pisa}. EdEngage’s scalable, personalized, and reasoning-focused approach directly addresses these challenges.

\begin{table}[h]
\centering
\caption{Recent Declines in Math Achievement}
\begin{tabular}{lcc}
\toprule
\textbf{Assessment} & \textbf{Score Change} & \textbf{Years} \\
\midrule
NAEP 4th Grade Math & -5 points & 2019–2022 \\
NAEP 8th Grade Math & -8 points & 2019–2022 \\
PISA Math (OECD Avg.) & -15 points & 2018–2022 \\
\bottomrule
\end{tabular}
\end{table}

\subsection*{Instrumental vs. Relational Understanding}

Skemp’s seminal distinction between instrumental (procedural) and relational (conceptual) understanding highlights a persistent educational challenge: students commonly demonstrate procedural fluency without deep conceptual comprehension~\cite{skemp1976understanding}. In the age of AI, we have the ultimate form of instrument. A tool so powerful that you don't even have to "plug-and-chug" into formulas you don't understand. There's an incredibly powerful AI that will do it for you in seconds. EdEngage explicitly focuses on Socratic dialogue, conceptual explanation, and relational understanding aligns by requiring students to explain their answers instead of just plugging them in. Further, but generating personalized questions based on the individual student's weaknesses, I hope to bridge concepts and help students form a deep, interconnected understanding of these concepts. By taking Skemp’s relational model into account, I hope to help foster knowledge transferability and deeper cognitive engagement.

Together, this evidence makes a compelling case for EdEngage as a necessary innovation in addressing contemporary gaps in mathematics education, emphasizing reasoning quality, equitable access, and sustainable, scalable personalization.


\section{Feature Selection}
Its clear that personalized learning is the way to go, but in order to personalize EdEngage to each student, I had to come up with a JSON object to track variables of interest. EdEngage’s student data schema is something that I had to research extensively, and have outlined in the following paper:
\begin{center}
\Large
\href{bit.ly/42oeVus}{bit.ly/42oeVus}
\end{center}

The features that I have chosen to track are deeply grounded in research from educational psychology, learning analytics, and instructional design. Effective personalization with LLMs isn't just about having more data; it’s about capturing the right features that genuinely impact learning outcomes. This means the featurues that will make our chatbot most effective, will give teachers the insights they need to better teach their classes, and make assignments enjoyable, process-based learning experiences for student.

For instance, detailed tracking of mastery and misconceptions comes directly from Bayesian Knowledge Tracing and related work showing fine-grained data boosts adaptive accuracy and student performance (\cite{Lee2023}). Similarly, preferences around learning modality and challenge levels are based on research confirming that aligning content format and difficulty with student needs reduces cognitive load and improves engagement (\cite{ElSabagh2021}, \cite{Bergdahl2024}). Tracking interests and preferred explanation styles isn’t superficial personalization—studies clearly link interest-driven content and tailored feedback with increased motivation and better understanding, particularly in math (\cite{VanDeWeijer2021}).

Including affective and motivational factors—like self-efficacy, mindset, and reaction to failure—is also research-driven. These dimensions significantly predict persistence, deep learning, and resilience (\cite{Maamin2022}, \cite{Ramaswami2023}). Behavioral analytics like hint usage, retry rates, and session engagement trends provide real-time signals for adaptive scaffolding, catching disengagement before it impacts performance (\cite{Bergdahl2024}, \cite{Maamin2022}).

Teacher notes, reading levels, and accommodations round out the schema, ensuring equitable access by addressing barriers that pure analytics might overlook (\cite{NGLC2021}, \cite{Boonen2016}). The resulting data structure ensures that every LLM interaction is genuinely responsive to each student's unique context, maximizing educational effectiveness rather than just automating superficial feedback.

Every feature in EdEngage is included for a clear, evidence-backed reason, ensuring the platform’s adaptivity is not just sophisticated but meaningful.



\section{Motivation for Agentic Alignment}

In education it is clear that model accuracy isn't just “nice to have”—it’s essential. If LLMs hallucinate in a classroom, a student can build a fragile understanding that’s tough to unravel later. That's why from the very first iteration of this project, alignment and explainability have driven every design choice. 

My original prototype was built around a simple agentic loop: EDEN, a Socratic math tutor, would answer student questions, but its responses were always double-checked by EVAL—a second agent designed to score for safety, clarity, and to block answer leakage. If EVAL flagged any rubric below threshold (say, EDEN revealed an answer, or the question wasn’t Socratic), specific feedback was given, and EDEN regenerated its answer with those issues in mind. Even with just these two agents, the system proved much harder to “game”: if a student tried to inject a prompt like “just give me the answer,” EVAL would catch and correct EDEN’s first flawed generation before a student ever saw it.

Please feel free to check it out here:
\begin{center}
\Large
\href{https://ed-engage-test.vercel.app/}{https://ed-engage-test.vercel.app/}
\end{center}
\normalsize
For me, this initial demo highlighted the power—and necessity—of multi-agent oversight for trustworthy AI tutoring. As the system evolved, it became obvious that more specialized agents would create not just safer responses, but richer analytics and more robust support for teachers.

Importantly, Agentic design isn’t just about “catching” bad outputs. It’s the only reliable way—short of exhaustive human oversight—to scale trustworthy, explainable, process-oriented tutoring. As the EdEngage demo showed, this approach both aligns AI to what matters for students, and surfaces the kind of learning analytics teachers say actually help.

\subsection*{System Architecture Overview}

\subsubsection*{Agentic Orchestration Motivation}

In math education, these shortcomings of a single chatbot system matter—a lot. Students need Socratic guidance, not just answers; teachers need clarity about where students got stuck and why; and the system itself must resist prompt injection, answer leakage, and drift from pedagogical guidelines.

Thus, I have scaled this system up with specialized agents to solve these problems by enforcing structure and division of labor. Each agent is responsible for a narrow educational or safety constraint—a setup that makes the system as a whole auditable and adaptive. For example, EDEN focuses strictly on Socratic questioning (one step at a time), EVAL ensures each tutor response follows the rubric (checks for leaks, alignment, and usefulness), ETHIC monitors student inputs for low effort or circumvention attempts, and so on. This agentic approach ensures misalignment or unexpected behaviors are caught and corrected before reaching students.


\subsubsection*{High-Level System Flow}

EdEngage's user journey is orchestrated by its central workflow engine, coordinating both student and teacher experiences.

\textbf{Student Flow:}
\begin{enumerate}
    \item \textbf{Intake:} The session begins with a brief quiz to capture learning style, interests, background, and any accommodations. Student profiles are initialized here.
    \item \textbf{Problem Generation:} ELICIT creates a problem tailored to the profile. It is then solved by the Wolfram Alpha Full Response API, which breaks it down into steps
    \item \textbf{Tutoring Loop:} For each question or step:
        \begin{itemize}
            \item ETHIC first reviews student input for effort/behavior concerns.
            \item If passed, ERGO identifies which math step is being worked on and scores understanding to be tracked for teachers and step specific personalization.
            \item EDEN responds, using Socratic questioning to help the student advance.
            \item EVAL audits EDEN’s output, enforcing alignment. If scores fall short, EDEN must regenerate.
            \item EMBED retrieves targeted notes/resources if requested.
        \end{itemize}
    \item \textbf{Session Summarization:} Afterward, EDIFY summarizes the session, capturing strengths, growth areas, and misconceptions. 
\end{enumerate}

\textbf{Teacher Flow:}
\begin{itemize}
    \item Teachers access dashboards that visualize mastery, misconceptions, and engagement across students and standards—using data straight from structured agent outputs.
    \item Teachers can upload notes, guide curriculum goals, or directly view conversation analytics for any student. One feature that was reccomended to me by a local Durham highschool teacher is highest/lowest understadning score queries. The lowest reveals what stuents in class might be thinking that may be off, while the high makes it very easy to tell if a student is using AI to interact with the chatbot.
\end{itemize}

\subsubsection*{System Modules and Dependencies}

EdEngage is organized to make each function maintainable, auditable, and tightly mapped to a research-backed educational goal:

\begin{itemize}
    \item \texttt{agents/} — Agent definitions for EDEN, EVAL, ETHIC, EDIFY, ELICIT, ERGO, and EMBED. Each module implements a specialized LLM agent with its own system prompt and output schema.
    \item \texttt{utils/} — Core orchestration, input validation, initialization, student modeling, reward loops, and profile management. Utilities connect agent outputs to meaningful analytics and enable agent hand-offs.
    \item \texttt{tools/} — Integrations with math computation engines (WolframAlpha), RAG components for resource retrieval, and pipelines for embedding teacher notes.
    \item \texttt{models/} — Typed schemas for all agent outputs, as well as richly structured student profiles and session histories.
    \item \texttt{tasks/} — Templates for orchestrating agent tasks, including step-by-step decompositions and sequencing logic.
    \item \texttt{ui/} — Console and teacher dashboard interfaces, optimized for classroom accessibility.
\end{itemize}

\begin{center}
\begin{adjustwidth}{-.5cm}{-4cm}
\begin{tabular}{|p{4cm}|p{6cm}|p{7cm}|}
\hline
\textbf{System Feature} & \textbf{Code Module(s)} & \textbf{Research Goal} \\
\hline
Personalized Socratic Tutoring & \texttt{agents/eden\_agent.py}, \texttt{models/output\_schemas.py} & Scaffolded, dialog-based reasoning \\
\hline
Step-Tracking, Misconception Scoring & \texttt{agents/ergo\_agent.py}, \texttt{utils/agent\_processor.py} & Explicit process evaluation; formative feedback \\
\hline
Effort/Behavior Moderation & \texttt{agents/ethic\_agent.py} & Equity; addressing avoidance/abuse patterns \\
\hline
Rubric-aligned Output Auditing & \texttt{agents/eval\_agent.py}, \texttt{utils/agent\_processor.py} & Alignment; safety and explainability \\
\hline
Automatic Session Analytics & \texttt{agents/edify\_agent.py}, \texttt{models/student\_profile.py} & Teacher insight; progress tracking \\
\hline
Adaptive Problem Generation & \texttt{agents/elicit\_agent.py}, \texttt{tools/math\_tools.py}, \texttt{models/student\_profile.py} & Individualization; responsive challenge \\
\hline
Resource Retrieval/Augmentation & \texttt{agents/embed\_agent.py}, \texttt{tools/rag\_tools.py} & Just-in-time supports; inclusion for diverse learners \\
\hline
Profile Management/Updating & \texttt{utils/profile\_manager.py}, \texttt{utils/profile\_updater.py} & Long-term growth modeling; actionable data \\
\hline
\end{tabular}
\end{adjustwidth}
\end{center}

Each component is designed so its reasoning, output, and adaptation logic is explainable and reviewable—supporting ongoing teacher feedback, alignment, and iterative improvement.

\subsection*{Pedagogical \& Personalization Model}

\subsubsection*{Socratic Dialogue and Stepwise Reasoning}

EdEngage puts Socratic pedagogy at the center of every tutoring interaction. Rather than delivering solutions, the system guides students through a sequence of small, targeted questions, each mapped to a single step in the underlying mathematical process. For any assignment, students must articulate their reasoning, justify next steps, and explain choices at each stage before moving forward. This sequence—pose, probe, reflect, iterate—ensures learning happens out loud, not in silence or by copying answers.

Hints and question prompts are central, but always tightly constrained. EDEN, the main tutor agent, can't reveal answers or skip steps; it is programmed and monitored to only generate nudges (“What might happen if you combine like terms here?”), plausibility checks, and invitations to self-reflection. Direct answers are blocked by both the agent's prompt structure \emph{and} by real-time rubric checks—EDEN's output is never delivered unless it passes a review by EVAL, which explicitly flags answer-leakage and enforces Socratic response norms.

Problems are decomposed into multiple subproblems and steps, often with the help of the ERGO agent. For example, solving a system of equations might be broken into: (1) setting up the system, (2) choosing a method, (3) carrying out substitution or elimination, (4) validating the result. This scaffolding means students practice thinking process-by-process, with every step an opportunity for formative feedback and correction.

\subsubsection*{Personalized Student Model}

Everything in EdEngage is organized around a structured, persistent \texttt{StudentProfile} object.
\begin{adjustwidth}{-2cm}{-2cm}
    
\begin{verbatim}
{
  "student_id": "12345",
  "student_profile": {
    "name": "Alex",
    "grade": "8th",
    "attention_span": "medium",
    "engagement_level": "higher with visual content",
    "modality_preference": "visual",
    "interests": ["space exploration", "video games", "drawing"],
    "preferred_explanation_methods": ["visual diagrams", "step-by-step breakdowns", "real-world examples"],
    "challenge_preference": "moderate",
    "tutoring_style_preference": "step-by-step guidance",
    "optimal_difficulty_level": 7,
    "collaboration_preference": "Prefers working alone or one-on-one, hesitant in groups",
    "personality_notes": {
      "communication_style": "Prefers direct questions and concise answers; comfortable indicating confusion",
      "self_assessment_ability": "Developing metacognitive awareness; occasionally can clearly identify gaps"
    },
    "academic_performance": {
      "strengths": {
        "algebra": {
          "average_score": 85,
          "correct_first_try_percentage": 78,
          "notes": "Strong grasp of concepts; occasional minor arithmetic mistakes"
        },
        "pattern recognition": {
          "notes": "Consistently identifies and extends patterns accurately"
        }
      },
      "areas_for_improvement": {
        "geometry": {
          "average_score": 70,
          "missed_first_try_percentage": 40,
          "notes": "Struggles especially with spatial reasoning and theorem application"
        },
        "word problems": {
          "average_score": 68,
          "missed_first_try_percentage": 50,
          "notes": "Often misinterprets wording; difficulties translating to mathematical expressions"
        },
        "showing_work": {
          "notes": "Regularly skips intermediate steps, even when answers are correct"
        }
      },
      "historical_scores": {
        "algebra": 85,
        "geometry": 70,
        "statistics": 75,
        "fractions": 82,
        "word_problems": 68
      }
    }
  },
  "knowledge_profile": {
    "mastery_levels": {
      "NC.8.EE.5": 0.85,
      "NC.8.NS.2": 0.60,
      "NC.8.G.2": 0.70
    },
    "skill_correctness": {
      "algebra": 78,
      "geometry": 60,
      "word_problems": 50
    },
    "misconceptions": [
      {
        "concept": "Negative Numbers",
        "issue": "Mistakes -(-x) as negative",
        "lastObserved": "2025-04-15"
      },
      {
        "concept": "Fractions",
        "issue": "Adds denominators instead of finding LCM",
        "lastObserved": "2025-04-10"
      }
    ],
    "identified_problem_solving_strategies": [
      {
        "strategy": "Systematic algebraic manipulation",
        "notes": "Usually effective; tends to skip steps occasionally",
        "lastObserved": "2025-04-12"
      },
      {
        "strategy": "Guessing approach",
        "notes": "Frequently guesses on word problems instead of structured attempts",
        "lastObserved": "2025-04-08"
      }
    ],
    "performance_history": [
      {"date": "2025-03-01", "assessment": "Geometry Unit Test", "score_percent": 75},
      {"date": "2025-04-01", "assessment": "Linear Equations Quiz", "score_percent": 85}
    ],
    "progress_trend": "improving"
  },
  "engagement_profile": {
    "weekly_active_minutes": 120,
    "avg_session_length_min": 15,
    "sessions_per_week": 3,
    “RAG_usage_rate": 0.25,
    “correct_first_try”: 0.70,
    "skip_rate": 0.10,
    "engagement_trend": "declining",
    "average_response_time_sec": 8,
    "idle_time_percentage": 10,
    "highest_comprehension_chat": {
      "message": "I substituted correctly this time and found x quickly!",
      "score": 4.6,
      "date": "2024-04-12"
    },
    "lowest_comprehension_chat": {
      "message": "I don't know what numbers go where in the equation.",
      "score": 2.5,
      "date": "2024-04-05"
    }
  },
  "affective_profile": {
    "interest_level": 4,
    "self_efficacy_score": 0.7,
    "goal_orientation": "Mastery",
    "mindset": "Growth",
    "math_anxiety": "Moderate",
    "reaction_to_failure": "Sometimes frustrated; generally persists"
  },
  "teacher_inputs": {
    "teacher_notes": "Highly participative in class discussions; struggles with multi-step problems but engages deeply with real-life examples.",
    "accommodations": ["preferential seating", "extra check-ins for understanding on word problems"],
    "reading_level": "Grade 7",
    "ELL_status": false,
    "learning_environment_needs": ["Quiet workspace recommended", "Extended time allowed on tests"]
  },
  "session_history": [
    {
      "date": "2024-04-05",
      "topic": "linear equations",
      "overall_understanding_score": 4.2,
      "strengths": ["isolating variables", "simple linear equations"],
      "weaknesses": ["translating word problems to equations"],
      "steps": [
        {
          "step_description": "Setting up equations from word problems",
          "hard_skills": ["equation formulation"],
          "soft_skills": ["reading comprehension"],
          "step_score": 3.0
        },
        {
          "step_description": "Isolating variables",
          "hard_skills": ["algebraic manipulation"],
          "soft_skills": ["attention to detail"],
          "step_score": 4.8
        }
      ],
      "highest_comprehension_chat": {
        "student_message": "I moved the x-term to the other side and remembered to change the sign.",
        "score": 4.9
      },
      "lowest_comprehension_chat": {
        "student_message": "I don't know what numbers go where in the equation from this problem.",
        "score": 2.5
      },
      "number_of_student_chats": 12,
      "engagement_score": "high"
    },
    {
      "date": "2024-04-08",
      "topic": "graphing linear equations",
      "overall_understanding_score": 3.8,
      "strengths": ["plotting points"],
      "weaknesses": ["interpreting slope and y-intercept", "connecting algebraic equations to graphs"],
      "steps": [
        {
          "step_description": "Calculating slope",
          "hard_skills": ["slope calculation"],
          "soft_skills": ["conceptual understanding"],
          "step_score": 3.2
        },
        {
          "step_description": "Identifying y-intercept from equations",
          "hard_skills": ["interpretation of equations"],
          "soft_skills": ["conceptual linkage"],
          "step_score": 3.0
        }
      ],
      "highest_comprehension_chat": {
        "student_message": "I plotted the points correctly, is the line supposed to slope up since it's positive?",
        "score": 4.5
      },
      "lowest_comprehension_chat": {
        "student_message": "What's slope again? Is it the number in front of x?",
        "score": 2.8
      },
      "number_of_student_chats": 15,
      "engagement_score": "medium-high"
    },
    {
      "date": "2024-04-12",
      "topic": "systems of equations",
      "overall_understanding_score": 3.5,
      "strengths": ["substitution method"],
      "weaknesses": ["elimination method", "recognizing cases of infinite/no solutions"],
      "steps": [
        {
          "step_description": "Using substitution method",
          "hard_skills": ["algebraic substitution"],
          "soft_skills": ["logical sequencing"],
          "step_score": 4.0
        },
        {
          "step_description": "Applying elimination method",
          "hard_skills": ["algebraic manipulation", "arithmetic precision"],
          "soft_skills": ["strategic thinking"],
          "step_score": 3.0
        }
      ],
      "highest_comprehension_chat": {
        "student_message": "I substituted correctly this time and found x quickly!",
        "score": 4.6
      },
      "lowest_comprehension_chat": {
        "student_message": "Why doesn't elimination work when the numbers don't cancel out?",
        "score": 2.7
      },
      "number_of_student_chats": 14,
      "engagement_score": "medium"
    }
  ],
  "performance_metrics": {
    "average_mistakes_first_attempt": {
      "algebra": 22,
      "geometry": 40,
      "word_problems": 50
    },
    "total_number_of_student_chats": 41,
    "flagged_for_attention": {
      "early_problem_mistakes": true,
      "frequent_repeated_mistakes": ["geometry", "word problems"]
    }
  },
  "indicators": {
    "peer_percentile": 40,
    "on_grade_level": false,
    "alerts": ["Engagement dropped recently", "Mastery of NC.8.NS.2 low after multiple attempts"]
  },
  "class_summary_insights": {
    "common_questions": ["What does slope represent?", "How to set up equations from word problems?"],
    "keywords": ["slope", "y-intercept", "equation setup", "negative numbers"],
    "class_strengths": ["algebraic substitution", "simple equation solving"],
    "class_weaknesses": ["interpreting word problems", "negative number operations"],
    "frequent_stuck_steps": ["initial equation setup from text", "identifying solution types for systems of equations"]
  },
  "future_recommendations": {
    "focus_next_session": ["geometry basics", "word problem-solving strategies"],
    "skills_to_build": ["metacognitive skills", "text-to-math translation"],
    "recommended_activities": ["visual-based geometry exercises", "guided reflective problem-solving"]
  },
  "formative_assessment_highlights": {
    "student_reflections": {
      "responses": [
        "I understand algebra but geometry feels confusing",
        "Word problems are difficult to set up"
      ],
      "suggested_followups": [
        "Why does geometry feel harder?",
        "Explain your thought process on word problems."
      ]
    },
    "teacher_summative_note": "Strong computational skills. Needs structured reflection tasks for better metacognition."
  }
}


\end{verbatim}
\end{adjustwidth}

\noindent


Student modeling happens at three timescales: intake (via quiz and teacher input); in-session (as each agent analyzes dialog responses, effort, and engagement using agent outputs); and longitudinally (with persistent updates after each session). Data integrated includes not just academic performance, but affective data (motivation, anxiety), cognitive traits (attention span, learning style), preferences (modality, challenge), and history (misconceptions, performance trends).

\subsection{Data Provenance for Student Profile JSON}

Different parts of the EdEngage system generate or enrich the student data object at different stages. The following table details where each core field in the EdEngage student profile JSON comes from—whether it is initialized by the Intake Quiz, created or updated by the ERGO agent, computed by System Informatics routines, or produced by the EDIFY analytics agent. This organization helps ensure every data point is explainable, auditable, and up-to-date for robust personalization, analytics, and reporting.

\footenotesize 

\subsubsection*{Fields Sourced from Intake Quiz}

\begin{adjustwidth}{-2cm}{-2cm}
\begin{center}
\begin{tabular}{|p{8cm}|p{5cm}|p{4cm}|}
\hline
\textbf{Profile Field} & \textbf{How/When Initialized} & \textbf{Notes} \\
\hline
student\_id & Registration/user entry & Permanent student identifier \\
student\_profile.name & Registration/user entry & Student-chosen name \\
student\_profile.grade & User selection & Grade level \\
student\_profile.attention\_span & Quiz question & Focus duration preference \\
student\_profile.engagement\_level & Quiz question & Self-reported, used for personalization \\
student\_profile.modality\_preference & Quiz question & E.g. Visual, auditory, etc.\\
student\_profile.interests & Quiz question or open text & Used for batch or themed problem generation \\
student\_profile.preferred\_explanation\_methods & Quiz question & Aids tutor prompt adaptation \\
student\_profile.challenge\_preference & Quiz question & Sets scaffolding default \\
student\_profile.tutoring\_style\_preference & Quiz question & Matches Socratic/Direct/Multi modes \\
student\_profile.optimal\_difficulty\_level & Derived from preference & Sets initial challenge, 1--10 scale \\
student\_profile.collaboration\_preference & Quiz question & For single/group settings \\
student\_profile.personality\_notes.communication\_style & Quiz question & Notes for agent adaptation \\
student\_profile.personality\_notes.self\_assessment\_ability & Quiz question & Used for metacognition prompts \\
affective\_profile.interest\_level & Quiz question & Motivation/curiosity baseline \\
affective\_profile.goal\_orientation & Quiz question & ``Mastery''/``Performance''/etc. \\
affective\_profile.mindset & Quiz question & Fixed/growth spectrum \\
affective\_profile.math\_anxiety & Quiz question & Mapped to support triggers \\
teacher\_inputs.teacher\_notes & Direct teacher entry & For context/personalization \\
teacher\_inputs.accommodations & Teacher entry & E.g. IEP, ELL needs \\
teacher\_inputs.reading\_level & Derived/entered & For material adaptation \\
teacher\_inputs.ELL\_status & Teacher flag & For language accommodations \\
teacher\_inputs.learning\_environment\_needs & Teacher entry or selection & E.g. audio, visual, attention... \\
\hline
\end{tabular}
\end{center}
\end{adjustwidth}

\vspace{1.2em}

\subsubsection*{Fields Sourced from ERGO Agent}

\begin{adjustwidth}{-2cm}{-2cm}
\begin{center}
\begin{tabular}{|p{8cm}|p{5cm}|p{4cm}|}
\hline
\textbf{Profile Field} & \textbf{How/When Initialized} & \textbf{Notes} \\
\hline
session\_history & Step/session completion & Sequential log of problems/steps \\
knowledge\_profile.misconceptions & Post-interaction analysis & Auto-flagged by ERGO for each session \\
knowledge\_profile.identified\_problem\_solving\_strategies & Post-interaction & E.g. elimination, visual, etc. \\
engagement\_profile.highest\_comprehension\_chat & After each session & Best-scoring interaction \\
engagement\_profile.lowest\_comprehension\_chat & After each session & Most challenged exchange \\
\hline
\end{tabular}
\end{center}
\end{adjustwidth}

\vspace{1.2em}

\subsubsection*{Fields Sourced from System Informatics}

\begin{adjustwidth}{-2cm}{-2cm}
\begin{center}
\begin{tabular}{|p{8cm}|p{5cm}|p{4cm}|}
\hline
\textbf{Profile Field} & \textbf{How/When Initialized} & \textbf{Notes} \\
\hline
engagement\_profile.weekly\_active\_minutes & Rolling update post-session & Minutes spent in system \\
engagement\_profile.avg\_session\_length\_min & Rolling average & Tracked over sessions \\
engagement\_profile.sessions\_per\_week & Rolling count & Used for engagement metrics \\
engagement\_profile.RAG\_usage\_rate & Logged each use & Rate at which resources are pulled \\
engagement\_profile.correct\_first\_try & Success on first attempt & For mastery/effort reporting \\
engagement\_profile.skip\_rate & After session & For disengagement flags \\
engagement\_profile.average\_response\_time\_sec & After each session & Averaged/aggregated metric \\
engagement\_profile.idle\_time\_percentage & During/after session & Percent idle/active \\
knowledge\_profile.mastery\_levels & Updated after problems & Mastery by standard/strand \\
knowledge\_profile.skill\_correctness.algebra & After each algebra-related item & Scored in mastery calculation \\
knowledge\_profile.skill\_correctness.geometry & After each geometry-related item & \\
knowledge\_profile.skill\_correctness.word\_problems & After word problem steps & \\
performance\_metrics.average\_mistakes\_first\_attempt.algebra & Post-problem per type & For effort analytics \\
performance\_metrics.average\_mistakes\_first\_attempt.geometry & Post-problem per type & \\
performance\_metrics.average\_mistakes\_first\_attempt.word\_problems & Post-problem per type & \\
performance\_metrics.flagged\_for\_attention & After session/step & Used to escalate for review \\
student\_profile.academic\_performance.historical\_scores & Aggregated & Longitudinal view for teacher \\
performance\_metrics.total\_number\_of\_student\_chats & Summed post-session & Used for engagement/effort \\
indicators.peer\_percentile & Benchmarking & Relative to peers (computed) \\
indicators.on\_grade\_level & Benchmarking & Grade-level performance flag \\
\hline
\end{tabular}
\end{center}
\end{adjustwidth}

\vspace{1.2em}

\subsubsection*{Fields Sourced from EDIFY Agent}

\begin{adjustwidth}{-2cm}{-2cm}
\begin{center}
\begin{tabular}{|p{8cm}|p{5cm}|p{4cm}|}
\hline
\textbf{Profile Field} & \textbf{How/When Initialized} & \textbf{Notes} \\
\hline
student\_profile.academic\_performance.strengths & Post-session/synthesis & Agent-classified \\
student\_profile.academic\_performance.areas\_for\_improvement & Post-session/synthesis & Weak skill/concept areas \\
knowledge\_profile.performance\_history & Rolling/longitudinal & Time-stamped summary \\
knowledge\_profile.progress\_trend & After each session & Improving/declining/steady \\
engagement\_profile.engagement\_trend & After each session & Directional engagement changes \\
indicators.alerts & After each session & For teacher or admin review \\
class\_summary\_insights & Aggregated periodically & Synthesized class/group data \\
future\_recommendations & After session or sequence & Next steps for student \\
affective\_profile.self\_efficacy\_score & Detected and updated post-session & Adjusted by recent confidence/behavior \\
affective\_profile.reaction\_to\_failure & From session & Detected mindsets towards errors \\
formative\_assessment\_highlights & Post-session reflect/plan & Self or teacher-generated reflections \\
\hline
\end{tabular}
\end{center}
\end{adjustwidth}

\normalsize
\vspace{1em}
\noindent
\textit{Note:} Most fields can be refined by teacher override or administrative input, while all real-time updates and analytics are auditable and tied to the originating process for transparency.

Problem types, question scaffolding, and even the presentation of hints adapt in real time based on the profile. For example, a student who prefers visual learning will get prompts to draw a diagram; one flagged for low confidence gets extra encouragement. Teachers can add notes, set accommodations (e.g., ELL status), or upload alternative content, which feeds directly into the system's adaptation and retrieval-augmented generation processes.

\subsubsection{Equity and Inclusion by Design}

Adaptation for equity is a first-class concern throughout EdEngage. Intake and profile routines explicitly capture learning needs, language preferences, attention markers, and environment constraints. Problem and hint content is generated in multiple modalities, and the system can switch between text, visuals, or even multilingual prompts if required.

Bias mitigation is not just retrofitted. ETHIC enforces equitable dialog by flagging language, effort, or avoidance patterns, without over-penalizing students who struggle with expression or speed. All analytics and adaptation points are regularly audited, and teacher dashboards disaggregate performance by demographic factors so teachers can spot—and intervene on—persistent disparities.

Error-tolerance is deliberately high: the system always prioritizes persistence, retrying, and student's providing evidence of growth over speed or accuracy alone. Every agent output and student metric feeds into adaptive scaffolding, not punitive feedback. Teacher notes and accommodations can override defaults and are surfaced to agents during every interaction, ensuring even students with uncommon needs are actively supported.

\subsection*{Technical Implementation Details}

\subsubsection{Backend System Infrastructure}

EdEngage’s backend uses a modular microservices approach, with each agent (EDEN, EVAL, ETHIC, ERGO, etc.) implemented as a distinct component. CrewAI is central to orchestrating the sequential, auditable workflow: CrewAI lets us define “crews” of agents where outputs can be chained, independently validated, or cascaded across tasks—enforcing handoffs and agent-specialized responsibilities at every step. Each agent’s input and output adheres to strict \texttt{pydantic} schemas. This both improves type-safety and makes it easy to validate, log, and audit agent behavior at runtime, ensuring all interaction data (from student queries to rubric scores) is structured and interpretable for downstream analytics.

Backend integrations are handled as follows:
\begin{itemize}
\item \textbf{LLM Providers:} The system is API-driven and supports OpenAI GPT models, Anthropic, Gemini, and others through environment-configured endpoints.
\item \textbf{Math/Computation:} Integrates directly with the Wolfram Alpha API for solution steps; features fallback logic to ensure robust student support even if computation fails.
\item \textbf{Knowledge Retrieval:} Uses FAISS vector search within a retrieval-augmented generation (RAG) pipeline for instant lookup of teacher-supplied notes and curriculum-aligned snippets.
\end{itemize}

All data workflows are FERPA- and COPPA-aware, with audit trails for every system interaction, fine-grained role-based access, and robust encryption.

\subsubsection*{Frontend Architecture}

Currently, EdEngage’s user experience is delivered through a terminal-based Python console interface. This allows for rapid iteration in early pilots and makes the system hardware-accessible even on low-resource devices. The terminal UI supports basic menu navigation, standards selection, and guided chat with all core agents.

Currently, I am working to migrate this backend to a modern Flask web front end. The new UI will allow for scalable classroom deployment, user session management, and richer, more accessible interaction designs drawn from the current Figma mockups.

The planned web UI (see Figma designs below) will feature keyboard navigation, high-contrast modes, screen reader support, and multilingual options tailored per student profile and device constraints. I plan to optomize the web app to deliver fast performance on Chromebooks, PCs, and tablets without additional plugins or high system requirements, since tehse are most common in classrooms.

\subsubsection{Extensibility \& Integration}

EdEngage is built for modular research and school integrations. LMS interoperability is achieved through CSV, LTI, and REST API connectors for rostering, performance exports, and teacher or admin controls. CrewAI crews and workflows are defined in configuration, allowing new agents and pipelines to be swapped in for experiments or curriculum expansion without refactoring. LLM/model providers and knowledge tools are selected via environment variables, making it easy to pilot new models or adapt deployments as needed. All outputs use open \texttt{pydantic} schemas and JSON for smooth analytics downstream and for teacher or admin review.

\subsection{Alignment, Equity, and Safety Considerations}

EdEngage is designed around alignment and equity, prioritizing safety and clear oversight at every level. Key agent outputs—especially from ETHIC (effort/behavior) and EVAL (response evaluation)—are logged with explanations and linked to the student's profile. When these agents flag a response (for low effort, potential bypass, inappropriate input, or accidental answer leakage), both the original content and the flagged reason are recorded in an auditable log. Teachers can review, override, or annotate any system decision, ensuring that no AI intervention happens as a black box.

\subsubsection*{Model Fine-tuning: MathDial and TutorEval Datasets}

To increase the instructional quality and alignment of EdEngage’s tutor and evaluator agents, I fine-tuned both EDEN (Socratic tutor) and EVAL (rubric checker) on established, purpose-built educational datasets. In the future, I would like to produce my own fine tuning dataset based off of teacher's responses and the data that is collected from students.

Firstly, The EDEN tutoring agent was fine-tuned on the MathDial dataset~\cite{macina2023}, which consists of 3,000 one-to-one, multi-step, grounded teacher-student math dialogues. These dialogues were generated by pairing human teachers with LLMs prompted to represent common student errors, ensuring that the teacher responses go beyond just solving a problem—they scaffold real student thinking using a range of research-based teacher moves. This dataset provides diverse and annotated examples of Socratic, process-focused dialogue, and minimizes the risk of training on shortcuts or leaky answers. MathDial, unlike crowdsourced or privacy-constrained sources, directly addresses the challenge of obtaining high-quality, realistic tutorial conversations and enables fine-tuned models to learn what it means to teach—not just to solve.

Then, for the EVAL response-checker agent, I used the TutorEval dataset as a fine-tuning and calibration benchmark. TutorEval consists of expert-written question-answering data and dialogue examples grounded in long-form STEM textbook chapters. These data challenge models to reason over extended contexts and evaluate the appropriateness, clarity, and correctness of tutoring responses, not just isolated Q\&A. TutorEval and its related TutorChat data ensure EVAL can robustly assess tutoring dialogue in line with real classroom use, across a wide range of mathematics and science topics.

To continually improve my models, I rigorously log all EdEngage tutor-student interactions—both the input context and agent outputs—along with rubric-aligned metadata and teacher/expert evaluations when available. This growing dataset will support further fine-tuning, RLHF, and targeted analysis, allowing me to adapt model behavior in response to real student needs and observed system errors over time.

All agent-generated feedback, classifications, and scoring are stored alongside clear rationales. Teacher dashboards display these agent rationales (“why a student response was flagged as low effort,” “why a tutor response needed revision”) directly, both for individual students and for whole-class trends. This ensures teachers can see exactly how and why the system intervened or adapted—never hiding the "why" behind an opaque output. This way, if a teacher ever has a qualm with the system, it is easy for them to express this and that way I could reengineer the prompts in response.

To avoid reinforcing or amplifying inequities, EdEngage builds bias checks and mitigation directly into agent prompts, evaluation rubrics, and analytics. ETHIC's moderation rules are reviewed to avoid over-penalizing for cultural, linguistic, or learning difference–related expressions. All analytics can be disaggregated by demographic group, learning profile, or support status to monitor for unintended disparities. Teachers can flag misclassifications, and system logs support regular audits for persistent patterns.

One concern that I have right now is in aligning with FERPA and COPPA guidance. Since I used public APIs for this, this data is not secure. I would like to one day host my own model, using encrypted storage, strict role-based access controls, and an end-to-end auditable record of user and agent actions. Tgis way only authorized staff and teachers can access sensitive data, and students' raw interaction data are never used outside the scope of instruction, support, or human-supervised research.


\textbf{Flag Types, Rationales, and Review Pathways:}

\begin{adjustwidth}{-2cm}{-2cm}
\begin{center}
\begin{tabular}{|p{3cm}|p{4cm}|p{4cm}|p{4cm}|}
\hline
\textbf{Agent/Flag Type} & \textbf{Trigger Example} & \textbf{Rationale} & \textbf{Review Pathway} \\
\hline
ETHIC: \texttt{low\_effort} & Repetitive generic responses ("idk", blank) & Indicates minimal engagement or guessing & Teacher reviews; suggested prompt for specificity \\
\hline
ETHIC: \texttt{bypass} & Requests for solutions, e.g. “just give me the answer” & Attempts to shortcut learning process & Teacher notified; system prompts student to show work \\
\hline
ETHIC: \texttt{unrelated} & Off-topic input, e.g. “what’s your favorite color?” & Off-task behavior & May be ignored, flagged for pattern if recurrent \\
\hline
ETHIC: \texttt{abuse} & System manipulation, inappropriate language & Ensures platform safety/integrity & Administrator/teacher required to review actions \\
\hline
EVAL: \texttt{reveal\_answer} & Tutor reply gives full solution & Enforces Socratic/stepwise process & Blocked from student, logged, teacher can override if needed \\
\hline
EVAL: \texttt{questions} & Tutor reply lacks questioning, misaligned tone & Ensures quality, thought-provoking prompts & Teacher can review flagged sessions or adjust rubrics \\
\hline
\end{tabular}
\end{center}
\end{adjustwidth}
All interventions can be traced to a specific trigger, reason, and system action, with a clear path for teacher input or reversal. The result is a system that surfaces the thinking and reasoning behind every decision—and keeps teachers in the loop as the final arbiters of individual student support.

\section{Demo}
I am reluctant to open source this project as of now (and it wouldn't run anyway without my API keys and fine tuned assistant IDs), so here is a link to a demo video:

\href{https://drive.google.com/file/d/1VdYPAcNCHIoRMHcdyMFLOvjXW8j1HQXV/view?usp=sharing}{https://drive.google.com/file/d/1VdYPAcNCHIoRMHcdyMFLOvjXW8j1HQXV/view?usp=sharing}

\section{Next Steps}

The future UI updates have already been mentioned in earlier sections, but in terms of the backend, the next step I want to take is to build a more powerful time-based profiles, flagging not only what a student needs now, but also how their trajectory compares to their own past and to class-level norms. I have an idea for an agent called EVOLVE that will take into account a students current profile, and then update it in a meaningful way that reflects their trajectory more clearly, instead of just appending updates to their profile, since this can easily get cluttered.

Reinforcement learning is the next major technical milestone. I tried out a basic rule-based system with Q-table updates, but its really can't do much for frozen models like OpenAI's. I want to shift towards open source models so that I can have control over the weights, and thus implement scalable RL loops (GRPO would be awesome) so agent behavior and rewards (e.g. for Socratic quality, equity, persistence) can be tuned continuously from teacher-labeled and student interaction data—without compromising safety or explainability.

On the teacher side, planned work includes new analytics visualizations and action panels: making it easier to cluster misconceptions, flag at-risk students, and directly assign or adjust scaffolding. Customizable alerts, exportable trend reports, and per-student recommendation engines will make EdEngage more actionable for overwhelmed classrooms.

Multilingual and multimodal expansions are also on the horizon. Architecturally, EdEngage is ready to support multilingual prompts and adaptive language models; the next step is partnering with ELL-focused classrooms and integrating fine-tuned translations. For multimodal, I plan to add richer visual scaffolds and support for image-based input, so students can draw, upload, or annotate as part of their process.

I also really want to pilot this to get some real world validation. I currently tutor 9 students through a tutoring company I helped start with my friend, as well as weekly tutoring through CHANCE with undeserved students. By partnering with these groups, I want to be able to provide this service to students who really need it. With student/parent permission, I would like to give access to EdEngage as a resource in exchange for trasncripts of live tutoring sessions from these groups to further fine tune EDEN with. By deploying in this setting, I also hope to get feedback as to how this system can improve.


There are many open challenges, from handling edge-case language tp effort-bias in diverse classrooms. I want to be able to translate sophisticated analytics into interventions that fit real classrooms without additional teacher burden. I hope to address these by wrking directly with educators from start to finish, and if this eventually gets funding I would like to compensate teachers for gold-standard human labels for fine tuning and using feedback to continually adjust both rubrics and system defaults. This user-centered iteration, grounded in real piloting, is the foundation for this project’s roadmap.


\section{Conclusion}

Our education system has an incentive problem. For too long, students have been rewarded for getting the right answer quickly rather than demonstrating deep understanding. Mistakes are penalized, not seen as a prompt for personalized support—unless a student has access to a private tutor. This structure discourages risk-taking, stifles curiosity, and teaches many students that their process doesn’t matter. More and more, students turn to online tools or chatbots for quick answers, bypassing the learning journey entirely. While technology has made knowledge more accessible, its benefits have concentrated among students already confident or privileged enough to pursue self-driven curiosity.

Today’s most widely used edtech platforms don’t address this fundamental reality. Tools like Khan Academy prioritize final answers and fail to require or capture how students think, even as research shows that verbalizing reasoning dramatically improves outcomes. Optional chatbots and hints are easily sidestepped by students under pressure or lacking confidence—these aren’t signs of laziness, but symptoms of a system that unintentionally incentivizes shortcuts and quiet avoidance. Newer AI platforms claim to personalize, but often focus on the endpoint, not the process, leaving the students who struggle most with the least actionable support. If teachers can’t see where thinking breaks down, they can’t address learning gaps or encourage productive struggle.

EdEngage was built to change this. Instead of focusing on the answer, this project focuses on the how and why—turning assignments into process-based, Socratic conversations that bring thinking to the surface. Our multi-agent approach not only guards against shortcuts and answer leakage but makes each student’s effort, misconception, and growth visible and actionable for teachers. By centering equity, explainability, and deep process, the project ensures that students who don't have access to high quality private instruction aren’t left behind or overlooked.

My vision with this project is a future where every student, regardless of background or starting point, has the opportunity learns with curiosity and confidence—supported by AI that helps them think, not just finish their homework. EdEngage is designed to make that future possible: giving students the chance to build real understanding, and teachers the insight they need to help make that happen.

