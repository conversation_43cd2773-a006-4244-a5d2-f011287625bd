import os
from pathlib import Path

output_file = "frontend_codebase_dump.md"
root_dir = "."

INCLUDED_EXTENSIONS = {
    '.js': 'javascript',
    '.mjs': 'javascript',
    '.ts': 'typescript',
    '.tsx': 'typescript',
    '.json': 'json',
    '.css': 'css',
    '.md': 'markdown',
    '.sql': 'sql'
}

EXCLUDED_DIRS = {'.next', 'node_modules'}
EXCLUDED_FILES = {'microservice-dump.txt'}

def build_tree(directory):
    tree_lines = []
    for root, dirs, files in os.walk(directory):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in EXCLUDED_DIRS and not d.startswith('.')]
        files.sort()
        dirs.sort()
        level = root.replace(directory, '').count(os.sep)
        indent = '│   ' * (level - 1) + ('├── ' if level > 0 else '')
        subdir = os.path.basename(root)
        if level == 0:
            tree_lines.append(f"{subdir}/")
        else:
            tree_lines.append(f"{indent}{subdir}/")
        for i, file in enumerate(files):
            if file.startswith('.') or file in EXCLUDED_FILES:
                continue
            file_indent = '│   ' * level + ('├── ' if i < len(files) - 1 else '└── ')
            tree_lines.append(f"{file_indent}{file}")
    return "\n".join(tree_lines)

def get_language_tag(filename):
    ext = Path(filename).suffix.lower()
    return INCLUDED_EXTENSIONS.get(ext, "")

with open(output_file, "w", encoding="utf-8") as out:
    # 1. Write directory tree
    out.write("## Project Structure\n\n")
    out.write("```\n")
    out.write(build_tree(root_dir))
    out.write("\n```\n\n")

    # 2. Write code for each included file type
    for foldername, subdirs, filenames in os.walk(root_dir):
        # Skip excluded dirs
        subdirs[:] = [d for d in subdirs if d not in EXCLUDED_DIRS and not d.startswith('.')]
        for filename in filenames:
            if filename.startswith('.') or filename in EXCLUDED_FILES:
                continue
            ext = Path(filename).suffix.lower()
            if ext in INCLUDED_EXTENSIONS:
                file_path = os.path.join(foldername, filename)
                rel_path = os.path.relpath(file_path, root_dir)
                lang_tag = get_language_tag(filename)
                out.write("\n---\n\n")
                out.write(f"### `{rel_path}`\n\n")
                out.write(f"```{lang_tag}\n")
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        out.write(f.read())
                except Exception as e:
                    out.write(f"// ERROR READING FILE: {e}")
                out.write("\n```\n")
