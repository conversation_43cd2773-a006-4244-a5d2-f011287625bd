# Microservice Demo Setup Guide

This guide will help you set up the complete pipeline for the chat and socratic microservice endpoints for your demo.

## 🎯 What This Provides

1. **Complete Data Pipeline**: Collects student profiles, chat history, and problem data
2. **Microservice Integration**: Ready-to-use endpoints for `/chat` and `/socratic`
3. **Response Storage**: Stores all microservice responses linked to users and sessions
4. **Demo Interface**: Working UI to test the complete flow

## 📋 Setup Steps

### 1. Database Schema Updates

Run the SQL script in your Supabase SQL editor:

```bash
# Copy the contents of microservice-schema-updates.sql
# Paste and run in Supabase > SQL Editor
```

This will:

- Add `microservice_payload` column to problems table
- Create `chat_responses` and `socratic_responses` tables
- Add sample problems with proper microservice format
- Set up indexes and RLS policies

### 2. Environment Variables

Make sure you have these in your `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Test the Demo

1. **Start your development server**:

   ```bash
   npm run dev
   ```

2. **Navigate to the demo page**:

   ```
   http://localhost:3000/demo/microservice
   ```

3. **Test the flow**:
   - Select a problem from the list
   - This creates a tutoring session
   - Send chat messages (general help)
   - Send socratic messages (step explanations)
   - View the conversation history

## 🔧 API Endpoints

### Chat Endpoint

```typescript
// tRPC endpoint: chat.sendChatMessage
{
  sessionId: string,
  problemId: string,
  userMessage: string
}
```

### Socratic Endpoint

```typescript
// tRPC endpoint: chat.sendSocraticMessage
{
  sessionId: string,
  problemId: string,
  studentExplanation: string,
  stepsCompleted: number[]
}
```

### Helper Endpoints

- `chat.getProblems` - Get available problems
- `chat.getOrCreateSession` - Create/get tutoring session
- `chat.getChatHistory` - Get conversation history

## 📊 Data Flow

### For Chat Endpoint:

1. **Input**: User message + session context
2. **Assembly**:
   - Student profile (from multiple tables)
   - Problem data (from microservice_payload)
   - Chat history (from chat_messages)
   - User message
3. **Output**: AI response stored in `chat_responses`

### For Socratic Endpoint:

1. **Input**: Student explanation + session context
2. **Assembly**:
   - Student profile
   - Problem with correct steps
   - Chat history
   - Student explanation
   - Steps completed
3. **Output**: Socratic response stored in `socratic_responses`

## 🗃️ Database Tables

### New Tables:

- `chat_responses` - Stores `/chat` endpoint responses
- `socratic_responses` - Stores `/socratic` endpoint responses

### Updated Tables:

- `problems` - Added `microservice_payload` JSONB column

### Sample Problem Format:

The problems table uses these columns:

- `statement` - The problem text
- `expected_steps` - JSONB array of step descriptions
- `correct_answer` - JSONB with the expected answer
- `microservice_payload` - JSONB formatted for microservice

```json
{
  "statement": "Two objects are moving toward a common point...",
  "answer": "x = 6",
  "steps": [
    "Write an equation for ObjectA: yA = 2 - 1x",
    "Write an equation for ObjectB: yB = 8 - 2x",
    "Set the distances equal: 2 - 1x = 8 - 2x",
    "Add 2x to both sides: 2 + 1x = 8",
    "Subtract 2 from both sides: x = 6"
  ]
}
```

## 🔄 Connecting Real Microservice

Currently using mock responses. To connect real microservice:

1. **Update the TODO sections** in `src/server/api/routers/chat.ts`:

   ```typescript
   // Replace this:
   const mockResponse: ChatResponse = { ... };

   // With actual microservice call:
   const response = await fetch('your-microservice-url/chat', {
     method: 'POST',
     body: JSON.stringify(chatPayload),
     headers: { 'Content-Type': 'application/json' }
   });
   const chatResponse = await response.json();
   ```

2. **The payload assembly is already done** - just pass `chatPayload` or `socraticPayload` to your microservice

## 🧪 Testing

### Manual Testing:

1. Go to `/demo/microservice`
2. Select "Droid Delivery Mission" problem
3. Send chat message: "I need help getting started"
4. Send socratic message: "I think I need to write equations for each object"
5. Check database to see stored responses

### Database Verification:

```sql
-- Check stored responses
SELECT * FROM chat_responses ORDER BY created_at DESC LIMIT 5;
SELECT * FROM socratic_responses ORDER BY created_at DESC LIMIT 5;

-- Check chat history
SELECT * FROM chat_messages WHERE session_id = 'your-session-id' ORDER BY timestamp;
```

## 🚀 Ready for Demo

You now have:

- ✅ Complete data assembly for both endpoints
- ✅ Proper storage of all responses
- ✅ Working demo interface
- ✅ Sample problems in correct format
- ✅ All data linked to users and sessions

Just replace the mock responses with actual microservice calls when ready!

## 🔧 Troubleshooting

### Common Issues:

1. **"Table doesn't exist" errors**: Run the SQL script in Supabase
2. **Permission errors**: Check RLS policies are set up correctly
3. **Missing environment variables**: Verify `.env.local` has correct values
4. **TypeScript errors**: Run `npm run build` to check for issues

### Debug Mode:

Check browser console and server logs for detailed error messages. All functions include comprehensive error logging.
