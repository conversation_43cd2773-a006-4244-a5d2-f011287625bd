-- Microservice Integration Schema Updates
-- Run this in your Supabase SQL editor

-- Add microservice_payload column to problems table
ALTER TABLE public.problems 
ADD COLUMN IF NOT EXISTS microservice_payload JSONB DEFAULT '{}';

-- Create chat_responses table for storing /chat endpoint responses
CREATE TABLE IF NOT EXISTS public.chat_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES tutoring_sessions(id) ON DELETE CASCADE,
    message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
    
    -- Response from /chat endpoint
    reply TEXT NOT NULL,
    moderation_flagged BOOLEAN DEFAULT FALSE,
    moderation_response TEXT,
    agent_outputs JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create socratic_responses table for storing /socratic endpoint responses
CREATE TABLE IF NOT EXISTS public.socratic_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES tutoring_sessions(id) ON DELETE CASCADE,
    step_work_id UUID REFERENCES student_step_work(id) ON DELETE CASCADE,
    
    -- Response from /socratic endpoint
    reply TEXT NOT NULL,
    current_step INTEGER,
    steps_covered INTEGER[],
    next_expected INTEGER,
    all_steps_sufficient BOOLEAN DEFAULT FALSE,
    step_scores JSONB DEFAULT '[]',
    agent_outputs JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for new tables
CREATE INDEX IF NOT EXISTS idx_chat_responses_session_id ON public.chat_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_responses_message_id ON public.chat_responses(message_id);
CREATE INDEX IF NOT EXISTS idx_socratic_responses_session_id ON public.socratic_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_socratic_responses_step_work_id ON public.socratic_responses(step_work_id);
CREATE INDEX IF NOT EXISTS idx_problems_microservice_payload ON public.problems USING GIN(microservice_payload);

-- Add triggers for updated_at columns (if update function exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
        CREATE TRIGGER update_chat_responses_updated_at 
        BEFORE UPDATE ON chat_responses 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        
        CREATE TRIGGER update_socratic_responses_updated_at 
        BEFORE UPDATE ON socratic_responses 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Insert sample problems with microservice payloads for demo
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES
(
    uuid_generate_v4(),
    'Droid Delivery Mission',
    'Two objects are moving toward a common point from different starting distances. Object A starts 2 miles away from the point and moves 1 mile every hour. Object B starts 8 miles away and moves 2 miles every hour. Let y represent the distance each object is from the point after x hours. After how many hours will both objects be the same distance from the point?',
    'socratic_conceptual',
    '8.EE.8',
    5,
    '[
        "Write an equation for ObjectA: yA = 2 - 1x",
        "Write an equation for ObjectB: yB = 8 - 2x",
        "Set the distances equal: 2 - 1x = 8 - 2x",
        "Add 2x to both sides: 2 + 1x = 8",
        "Subtract 2 from both sides: x = 6"
    ]'::jsonb,
    '"x = 6"'::jsonb,
    '{
        "statement": "Two objects are moving toward a common point from different starting distances. Object A starts 2 miles away from the point and moves 1 mile every hour. Object B starts 8 miles away and moves 2 miles every hour. Let y represent the distance each object is from the point after x hours. After how many hours will both objects be the same distance from the point?",
        "answer": "x = 6",
        "steps": [
            "Write an equation for ObjectA: yA = 2 - 1x",
            "Write an equation for ObjectB: yB = 8 - 2x",
            "Set the distances equal: 2 - 1x = 8 - 2x",
            "Add 2x to both sides: 2 + 1x = 8",
            "Subtract 2 from both sides: x = 6"
        ]
    }'::jsonb
),
(
    uuid_generate_v4(),
    'Linear Equation Practice',
    'Solve for x: 3x + 5 = 14',
    'mechanical_steps',
    '8.EE.7',
    3,
    '[
        "Start with the equation: 3x + 5 = 14",
        "Subtract 5 from both sides: 3x = 9",
        "Divide both sides by 3: x = 3"
    ]'::jsonb,
    '"x = 3"'::jsonb,
    '{
        "statement": "Solve for x: 3x + 5 = 14",
        "answer": "x = 3",
        "steps": [
            "Start with the equation: 3x + 5 = 14",
            "Subtract 5 from both sides: 3x = 9",
            "Divide both sides by 3: x = 3"
        ]
    }'::jsonb
),
(
    uuid_generate_v4(),
    'System of Equations',
    'Find the intersection point of the lines y = 2x + 1 and y = -x + 7.',
    'socratic_conceptual',
    '8.EE.8',
    6,
    '[
        "Write the system: y = 2x + 1 and y = -x + 7",
        "Set equations equal: 2x + 1 = -x + 7",
        "Add x to both sides: 3x + 1 = 7",
        "Subtract 1 from both sides: 3x = 6",
        "Divide by 3: x = 2",
        "Substitute back: y = 2(2) + 1 = 5"
    ]'::jsonb,
    '"(2, 5)"'::jsonb,
    '{
        "statement": "Find the intersection point of the lines y = 2x + 1 and y = -x + 7.",
        "answer": "(2, 5)",
        "steps": [
            "Write the system: y = 2x + 1 and y = -x + 7",
            "Set equations equal: 2x + 1 = -x + 7",
            "Add x to both sides: 3x + 1 = 7",
            "Subtract 1 from both sides: 3x = 6",
            "Divide by 3: x = 2",
            "Substitute back: y = 2(2) + 1 = 5"
        ]
    }'::jsonb
)
ON CONFLICT (id) DO NOTHING;

-- Enable RLS on new tables
ALTER TABLE chat_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE socratic_responses ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for new tables
CREATE POLICY "Users can view their own chat responses" ON chat_responses 
FOR ALL USING (
    session_id IN (
        SELECT id FROM tutoring_sessions WHERE user_id = auth.uid()
    )
);

CREATE POLICY "Users can view their own socratic responses" ON socratic_responses 
FOR ALL USING (
    session_id IN (
        SELECT id FROM tutoring_sessions WHERE user_id = auth.uid()
    )
);

-- Grant necessary permissions
GRANT ALL ON chat_responses TO authenticated;
GRANT ALL ON socratic_responses TO authenticated;
-- Note: No sequences needed for UUID primary keys

