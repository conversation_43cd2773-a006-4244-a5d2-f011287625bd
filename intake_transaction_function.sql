-- =============================================
-- INTAKE FORM COMPLETION TRANSACTION FUNCTION
-- Handles atomic insertion into new schema tables
-- =============================================

CREATE OR REPLACE FUNCTION complete_student_intake(
    p_user_id UUID,
    p_email TEXT,
    p_name TEXT,
    p_grade TEXT,
    p_age INT,
    p_languages TEXT[],
    p_learning_style TEXT,
    p_challenge_preference TEXT,
    p_attention_span TEXT,
    p_math_confidence TEXT,
    p_challenging_topics TEXT[] DEFAULT '{}',
    p_explanation_methods TEXT[] DEFAULT '{}',
    p_interests TEXT[] DEFAULT '{}'
) RETURNS JSON AS $$
DECLARE
    v_result JSON;
    v_error_msg TEXT;
BEGIN
    -- Start transaction (implicit in function)
    
    -- 1. Upsert user record with updated information
    INSERT INTO public.users (id, email, full_name, role, created_at, updated_at)
    VALUES (p_user_id, p_email, p_name, 'student', NOW(), NOW())
    ON CONFLICT (id) 
    DO UPDATE SET 
        full_name = p_name,
        email = p_email,
        updated_at = NOW();

    -- 2. Upsert student profile
    INSERT INTO public.student_profiles (
        user_id, 
        name, 
        grade,
        age,
        languages,
        attention_span,
        modality_preference,
        challenge_preference,
        tutoring_style_preference,
        optimal_difficulty_level,
        created_at,
        updated_at
    ) VALUES (
        p_user_id,
        p_name,
        p_grade,
        p_age,
        p_languages,
        p_attention_span,
        p_learning_style,
        p_challenge_preference,
        CASE
            WHEN 'step_by_step' = ANY(p_explanation_methods)
            THEN 'step_by_step'
            ELSE 'socratic' -- Default or if 'step_by_step' isn't preferred
        END,
        5, -- default difficulty level
        NOW(),
        NOW()
    ) ON CONFLICT (user_id)
    DO UPDATE SET
        name = p_name,
        grade = p_grade,
        age = p_age,
        languages = p_languages,
        attention_span = p_attention_span,
        modality_preference = p_learning_style,
        challenge_preference = p_challenge_preference,
        tutoring_style_preference = CASE
            WHEN 'step_by_step' = ANY(p_explanation_methods)
            THEN 'step_by_step'
            ELSE student_profiles.tutoring_style_preference -- Keep existing on conflict if not specified
        END,
        updated_at = NOW();

    -- 3. Clear and insert student interests
    DELETE FROM public.student_interests WHERE user_id = p_user_id;
    IF array_length(p_interests, 1) > 0 THEN
        INSERT INTO public.student_interests (user_id, interest_name, created_at)
        SELECT p_user_id, unnest(p_interests), NOW();
    END IF;

    -- 4. Clear and insert explanation preferences  
    DELETE FROM public.student_explanation_preferences WHERE user_id = p_user_id;
    IF array_length(p_explanation_methods, 1) > 0 THEN
        INSERT INTO public.student_explanation_preferences (user_id, method_name, created_at)
        SELECT p_user_id, unnest(p_explanation_methods), NOW();
    END IF;

    -- 5. Upsert affective profile with math confidence mapping
    INSERT INTO public.student_affective_profiles (
        user_id,
        interest_level,
        self_efficacy_score,
        goal_orientation,
        mindset,
        math_anxiety_level,
        reaction_to_failure,
        updated_at
    ) VALUES (
        p_user_id,
        3, -- default interest level
        CASE p_math_confidence
            WHEN 'not_confident' THEN 0.1
            WHEN 'somewhat_unconfident' THEN 0.3
            WHEN 'neutral' THEN 0.5
            WHEN 'somewhat_confident' THEN 0.7
            WHEN 'very_confident' THEN 0.9
            ELSE 0.5
        END,
        'mastery',
        'growth',
        CASE p_math_confidence
            WHEN 'not_confident' THEN 'high'
            WHEN 'somewhat_unconfident' THEN 'moderate'
            WHEN 'neutral' THEN 'moderate'
            WHEN 'somewhat_confident' THEN 'low'
            WHEN 'very_confident' THEN 'low'
            ELSE 'moderate'
        END,
        'persistence',
        NOW()
    ) ON CONFLICT (user_id)
    DO UPDATE SET
        self_efficacy_score = CASE p_math_confidence
            WHEN 'not_confident' THEN 0.1
            WHEN 'somewhat_unconfident' THEN 0.3
            WHEN 'neutral' THEN 0.5
            WHEN 'somewhat_confident' THEN 0.7
            WHEN 'very_confident' THEN 0.9
            ELSE 0.5
        END,
        math_anxiety_level = CASE p_math_confidence
            WHEN 'not_confident' THEN 'high'
            WHEN 'somewhat_unconfident' THEN 'moderate'
            WHEN 'neutral' THEN 'moderate'
            WHEN 'somewhat_confident' THEN 'low'
            WHEN 'very_confident' THEN 'low'
            ELSE 'moderate'
        END,
        updated_at = NOW();

    -- 6. Insert challenging topics as initial misconceptions (if any)
    IF array_length(p_challenging_topics, 1) > 0 THEN
        INSERT INTO public.student_misconceptions (
            user_id,
            concept,
            misconception_description,
            confidence_score,
            first_observed,
            last_observed,
            times_observed,
            status,
            created_at
        )
        SELECT 
            p_user_id,
            unnest(p_challenging_topics),
            'Self-identified as challenging during intake',
            0.8, -- high confidence since self-reported
            NOW(),
            NOW(),
            1,
            'active',
            NOW()
        ON CONFLICT DO NOTHING; -- Don't duplicate if already exists
    END IF;

    -- 7. Create success response
    v_result := json_build_object(
        'success', true,
        'user_id', p_user_id,
        'message', 'Intake completed successfully',
        'timestamp', NOW()
    );

    RETURN v_result;

EXCEPTION WHEN OTHERS THEN
    -- Handle any errors
    v_error_msg := SQLERRM;
    
    -- Log error (you could insert into an error log table here)
    RAISE WARNING 'Intake completion error for user %: %', p_user_id, v_error_msg;
    
    -- Return error response
    v_result := json_build_object(
        'success', false,
        'error', 'Database error occurred',
        'timestamp', NOW()
    );
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION complete_student_intake TO authenticated;

-- Add comment
COMMENT ON FUNCTION complete_student_intake IS 'Atomically processes student intake form data using new schema structure'; 