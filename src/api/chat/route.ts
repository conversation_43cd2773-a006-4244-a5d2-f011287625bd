// src/api/chat/route.ts
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const studentMessage = body.message as string;
    const assignmentId = body.assignmentId as string;
    const questionId = body.questionId as string;
    const stepId = body.stepId as string | undefined;

    console.log(`Chat API received context:
        Message: "${studentMessage}"
        Assignment ID: ${assignmentId}
        Question ID: ${questionId}
        Step ID: ${stepId || "N/A"}
    `);

    let edenReply = "I'm processing your message...";
    const lowerMessage = studentMessage.toLowerCase();

    if (lowerMessage.includes("hello") || lowerMessage.includes("hi")) {
      edenReply = "Hello! How can I assist you with this problem or step?";
    } else if (lowerMessage.includes("stuck")) {
      edenReply =
        "No problem, it happens! Could you show me what you've tried so far for this step, or tell me what's confusing you?";
      if (stepId) {
        edenReply += ` (You're currently on: ${stepId}).`;
      }
    } else if (lowerMessage.includes("help") || lowerMessage.includes("?")) {
      edenReply =
        "I'm here to help! What specific part of the question or step are you thinking about?";
    } else if (
      lowerMessage.includes("answer for x") ||
      lowerMessage.includes("value of x")
    ) {
      edenReply =
        "I can't give you the direct answer, but I can help you work through the steps to find it! Where are you in your calculation?";
    } else if (lowerMessage.length > 40) {
      edenReply =
        "That's a detailed thought. Let's break it down. What's your current focus within that?";
    } else if (
      lowerMessage.length < 5 &&
      lowerMessage !== "ok" &&
      lowerMessage !== "yes" &&
      lowerMessage !== "no"
    ) {
      edenReply =
        "Could you please elaborate a bit more on your question or thought?";
    } else {
      edenReply =
        "Thanks for sharing. Keep up the good work! Let me know if a specific question comes up.";
    }

    // Simulate a network delay
    await new Promise((resolve) =>
      setTimeout(resolve, Math.random() * 800 + 200)
    ); // 200-1000ms delay

    return NextResponse.json({ reply: edenReply });
  } catch (error) {
    console.error("Chat API error:", error);
    return NextResponse.json(
      { error: "Failed to process chat message" },
      { status: 500 }
    );
  }
}
