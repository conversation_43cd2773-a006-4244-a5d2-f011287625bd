// src/lib/supabase/auth.ts
import { supabase } from "./client";
import type { Session, AuthError } from "@supabase/supabase-js";

/**
 * Send a magic-link to the user’s email.
 * Supabase will email a one-time login link that redirects back to your app.
 */
export async function sendMagicLink(
  email: string
): Promise<{ data: { session: Session | null }; error: AuthError | null }> {
  return supabase.auth.signInWithOtp({
    email,
    options: {
      // After clicking the magic link, they’ll come back here:
      emailRedirectTo: `${
        typeof window !== "undefined"
          ? window.location.origin
          : process.env.NEXT_PUBLIC_SITE_URL
      }/auth/callback`,
      shouldCreateUser: true,
    },
  });
}
