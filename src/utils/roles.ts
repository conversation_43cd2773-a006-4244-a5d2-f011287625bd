import { supabase } from "@/utils/supabase/client";

// Role types
export type UserRole = "student" | "teacher" | "admin";

/**
 * Get the user's role from the database
 */
export async function getUserRole(): Promise<UserRole | null> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) return null;

    // Check if user profile exists
    const { data: profile, error } = await supabase
      .from("users")
      .select("role")
      .eq("id", user.id)
      .maybeSingle();

    if (error) throw error;

    return (profile?.role as UserRole) || null;
  } catch (error) {
    console.error("Error fetching user role:", error);
    return null;
  }
}

/**
 * Create or update user profile with specified role
 */
export async function ensureUserProfile(
  userId: string,
  role: UserRole = "student",
  displayName?: string,
  email?: string
): Promise<boolean> {
  try {
    const { error } = await supabase.from("users").upsert({
      id: userId,
      email: email || "",
      role: role,
      full_name: displayName || undefined,
      updated_at: new Date().toISOString(),
    });

    return !error;
  } catch (error) {
    console.error("Error ensuring user profile:", error);
    return false;
  }
}

/**
 * Check if current user has admin role
 */
export async function isAdmin(): Promise<boolean> {
  const role = await getUserRole();
  return role === "admin";
}

/**
 * Check if current user has teacher role or above
 */
export async function isTeacherOrAdmin(): Promise<boolean> {
  const role = await getUserRole();
  return role === "teacher" || role === "admin";
}
