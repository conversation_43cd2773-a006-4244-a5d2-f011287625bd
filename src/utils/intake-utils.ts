// src/utils/intake-utils.ts
import { supabase } from "@/utils/supabase/client";

export interface StudentProfile {
  name: string;
  grade: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Check if a user has completed their student intake
 */
export async function hasCompletedIntake(userId: string): Promise<boolean> {
  try {
    const { data: studentProfile, error } = await supabase
      .from("student_profiles")
      .select("name, grade")
      .eq("user_id", userId)
      .maybeSingle();

    if (error) {
      console.error("Error checking intake completion:", error);
      return false;
    }

    // Consider intake completed if both name and grade are present
    return !!(studentProfile?.name && studentProfile?.grade);
  } catch (error) {
    console.error("Error checking intake completion:", error);
    return false;
  }
}

/**
 * Get student profile data
 */
export async function getStudentProfile(userId: string): Promise<StudentProfile | null> {
  try {
    const { data: studentProfile, error } = await supabase
      .from("student_profiles")
      .select("*")
      .eq("user_id", userId)
      .maybeSingle();

    if (error) {
      console.error("Error fetching student profile:", error);
      return null;
    }

    return studentProfile;
  } catch (error) {
    console.error("Error fetching student profile:", error);
    return null;
  }
}

/**
 * Create or update student profile
 */
export async function upsertStudentProfile(
  userId: string,
  profileData: Partial<StudentProfile>
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("student_profiles")
      .upsert({
        user_id: userId,
        ...profileData,
        updated_at: new Date().toISOString(),
      });

    if (error) {
      console.error("Error upserting student profile:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error upserting student profile:", error);
    return false;
  }
}

/**
 * Check if user should be redirected away from intake form
 * (i.e., they've already completed it)
 */
export async function shouldRedirectFromIntake(userId: string): Promise<string | null> {
  const completed = await hasCompletedIntake(userId);
  
  if (completed) {
    return "/student/dashboard"; // Redirect to dashboard if already completed
  }
  
  return null; // Allow access to intake form
}
