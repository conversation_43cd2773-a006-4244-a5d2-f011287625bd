import { type NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip for API routes, static files, and Next.js internals
  if (
    pathname.startsWith("/api/") ||
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.includes(".") ||
    pathname.startsWith("/test-auth") // Allow test routes
  ) {
    return NextResponse.next();
  }

  // Allow public routes without auth check
  if (pathname === "/" || pathname === "/auth/callback") {
    return NextResponse.next();
  }

  // For now, let's only handle the magic-link redirect and leave everything else to client-side
  if (pathname === "/auth/magic-link") {
    try {
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          auth: {
            flowType: "pkce",
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true,
          },
          cookies: {
            getAll: () => request.cookies.getAll(),
            setAll: () => {}, // Don't set cookies in middleware for now
          },
        }
      );

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        // User is authenticated, redirect to student dashboard
        const url = request.nextUrl.clone();
        url.pathname = "/student/dashboard";
        return NextResponse.redirect(url);
      }
    } catch (error) {
      console.error("Auth check error:", error);
    }
  }

  // For all other routes, let them through and let client-side handle auth
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/|favicon.ico|public/|api/).*)"],
};
