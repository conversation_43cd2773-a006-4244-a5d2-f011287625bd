// src/components/nav-bar.tsx
"use client";
import { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { getUserRole, UserRole } from "@/utils/roles";
import { useAuth } from "@/contexts/auth-context";
import Image from "next/image";

export default function NavBar() {
  const { user, signOut } = useAuth();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const pathname = usePathname();

  useEffect(() => {
    async function fetchUserRole() {
      if (user) {
        const role = await getUserRole();
        setUserRole(role);
      }
    }

    fetchUserRole();
  }, [user]);

  if (!user) return null;

  return (
    <header className="bg-blue-900 shadow-md">
      <div className="container mx-auto flex items-center justify-between p-4">
        <div className="flex items-center">
          <Link href="/student/dashboard">
            <button className="logoButton">EdEngage</button>
          </Link>
        </div>

        <nav className="flex items-center">
          <ul className="flex space-x-6 text-white">
            <li>
              <Link
                href="/student/dashboard"
                className={pathname === "/student/dashboard" ? "font-bold" : ""}
              >
                Dashboard
              </Link>
            </li>

            {/* Teacher and Admin Links */}
            {(userRole === "teacher" || userRole === "admin") && (
              <>
                <li>
                  <Link
                    href="/teacher/assignments"
                    className={
                      pathname.startsWith("/teacher/assignments")
                        ? "font-bold"
                        : ""
                    }
                  >
                    Manage Assignments
                  </Link>
                </li>
                <li>
                  <Link
                    href="/teacher/students"
                    className={
                      pathname.startsWith("/teacher/students")
                        ? "font-bold"
                        : ""
                    }
                  >
                    Students
                  </Link>
                </li>
              </>
            )}

            {/* Admin Only Links */}
            {userRole === "admin" && (
              <li>
                <Link
                  href="/admin/users"
                  className={
                    pathname.startsWith("/admin/users") ? "font-bold" : ""
                  }
                >
                  User Management
                </Link>
              </li>
            )}

            {/* User Controls */}
            <li>
              <Link
                href="/profile"
                className={pathname === "/profile" ? "font-bold" : ""}
              >
                Profile
              </Link>
            </li>
            <li>
              <button
                onClick={() => signOut()}
                className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              >
                Sign Out
              </button>
            </li>
          </ul>
        </nav>
      </div>
    </header>
  );
}
