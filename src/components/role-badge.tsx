// src/components/role-badge.tsx
import { UserRole } from "@/utils/roles";

interface RoleBadgeProps {
  role: UserRole;
  className?: string;
}

export default function RoleBadge({ role, className = "" }: RoleBadgeProps) {
  const baseClass = "inline-block rounded-full px-3 py-1 text-xs font-semibold";

  const colorClass = {
    student: "bg-blue-100 text-blue-800",
    teacher: "bg-green-100 text-green-800",
    admin: "bg-purple-100 text-purple-800",
  }[role];

  return (
    <span className={`${baseClass} ${colorClass} ${className}`}>
      {role.charAt(0).toUpperCase() + role.slice(1)}
    </span>
  );
}
