// src/components/protected-route.tsx
"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";

export default function ProtectedRoute({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isLoading } = useAuth();
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);

  // Track when we've initially loaded to prevent showing loading on subsequent auth checks
  useEffect(() => {
    if (!isLoading && user) {
      setHasInitiallyLoaded(true);
    }
  }, [isLoading, user]);

  // Simple redirect for unauthenticated users
  // Middleware handles most cases, this is just a backup
  useEffect(() => {
    if (!isLoading && !user && hasInitiallyLoaded) {
      // Only redirect after a delay to avoid conflicts
      const timeoutId = setTimeout(() => {
        if (!user) {
          window.location.href = "/auth/magic-link";
        }
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [user, isLoading, hasInitiallyLoaded]);

  // Show loading state only on initial load, not on subsequent auth refreshes
  if (isLoading && !hasInitiallyLoaded) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  // If authenticated, render children
  return user ? <>{children}</> : null;
}
