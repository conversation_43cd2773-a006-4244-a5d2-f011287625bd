// src/components/debug-panel.tsx
"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { useStudentProfile } from "@/hooks/use-student-profile";

export default function DebugPanel() {
  const [isVisible, setIsVisible] = useState(false);
  const { user, isLoading: authLoading } = useAuth();
  const { profileData, userRole, isLoading: profileLoading, error, lastFetchTime } = useStudentProfile();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-gray-800 text-white px-3 py-2 rounded-lg text-sm hover:bg-gray-700"
      >
        Debug
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-80 max-h-96 overflow-y-auto">
          <h3 className="font-bold text-lg mb-3">Debug Information</h3>
          
          <div className="space-y-3 text-sm">
            <div>
              <strong>Auth Status:</strong>
              <div className="ml-2">
                <p>Loading: {authLoading ? 'Yes' : 'No'}</p>
                <p>User ID: {user?.id || 'None'}</p>
                <p>Email: {user?.email || 'None'}</p>
              </div>
            </div>
            
            <div>
              <strong>Profile Status:</strong>
              <div className="ml-2">
                <p>Loading: {profileLoading ? 'Yes' : 'No'}</p>
                <p>Error: {error || 'None'}</p>
                <p>Role: {userRole || 'None'}</p>
                <p>Name: {profileData?.name || 'None'}</p>
                <p>Grade: {profileData?.grade || 'None'}</p>
                <p>Last Fetch: {lastFetchTime ? new Date(lastFetchTime).toLocaleTimeString() : 'Never'}</p>
              </div>
            </div>
            
            <div>
              <strong>Page State:</strong>
              <div className="ml-2">
                <p>Hidden: {document.hidden ? 'Yes' : 'No'}</p>
                <p>Focused: {document.hasFocus() ? 'Yes' : 'No'}</p>
                <p>Cache Age: {lastFetchTime ? `${Math.round((Date.now() - lastFetchTime) / 1000)}s` : 'N/A'}</p>
              </div>
            </div>
            
            <div>
              <strong>Performance:</strong>
              <div className="ml-2">
                <p>Tab Switching: Fixed</p>
                <p>Data Persistence: Active</p>
                <p>Cache Status: {lastFetchTime && (Date.now() - lastFetchTime) < 300000 ? 'Fresh' : 'Stale'}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
