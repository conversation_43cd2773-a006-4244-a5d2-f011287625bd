"use client";
import { useEffect, useState, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { getUserRole, UserRole } from "@/utils/roles";

interface RoleProtectedRouteProps {
  children: ReactNode;
  allowedRoles: UserRole[];
  redirectTo?: string;
}

export default function RoleProtectedRoute({
  children,
  allowedRoles,
  redirectTo = "/student/dashboard",
}: RoleProtectedRouteProps) {
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function checkAuthorization() {
      const userRole = await getUserRole();
      const authorized = userRole !== null && allowedRoles.includes(userRole);
      setIsAuthorized(authorized);

      if (userRole !== null && !authorized) {
        // User is logged in but doesn't have permission
        router.push(redirectTo);
      }
    }

    checkAuthorization();
  }, [allowedRoles, redirectTo, router]);

  // Loading state
  if (isAuthorized === null) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  // Render children only if authorized
  return isAuthorized ? <>{children}</> : null;
}

// Usage examples as additional exports
export function AdminRoute({ children }: { children: ReactNode }) {
  return (
    <RoleProtectedRoute allowedRoles={["admin"]}>{children}</RoleProtectedRoute>
  );
}

export function TeacherRoute({ children }: { children: ReactNode }) {
  return (
    <RoleProtectedRoute allowedRoles={["teacher", "admin"]}>
      {children}
    </RoleProtectedRoute>
  );
}
