"use client";

import { useState } from "react";
import { api } from "@/utils/api";

interface ChatExampleProps {
  assignmentId: string;
  questionId: string;
  stepId?: string;
}

export function ChatExample({
  assignmentId,
  questionId,
  stepId,
}: ChatExampleProps) {
  const [message, setMessage] = useState("");
  const [responses, setResponses] = useState<
    Array<{ role: "user" | "assistant"; content: string }>
  >([]);

  const sendMessage = api.chat.sendMessage.useMutation({
    onSuccess: (data) => {
      setResponses((prev) => [
        ...prev,
        { role: "assistant", content: data.reply },
      ]);
    },
    onError: (error) => {
      console.error("Failed to send message:", error);
      alert("Failed to send message. Please try again.");
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    // Add user message to responses
    setResponses((prev) => [...prev, { role: "user", content: message }]);

    // Send message via tRPC
    await sendMessage.mutateAsync({
      message,
      assignmentId,
      questionId,
      stepId,
      context: {
        previousMessages: responses.map((r) => ({
          role: r.role,
          content: r.content,
          timestamp: new Date().toISOString(),
        })),
      },
    });

    // Clear input
    setMessage("");
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {responses.map((response, idx) => (
          <div
            key={idx}
            className={`p-3 rounded-lg ${
              response.role === "user"
                ? "bg-blue-100 ml-auto max-w-[80%]"
                : "bg-gray-100 mr-auto max-w-[80%]"
            }`}
          >
            <p className="text-sm">{response.content}</p>
          </div>
        ))}
        {sendMessage.isPending && (
          <div className="bg-gray-100 mr-auto max-w-[80%] p-3 rounded-lg">
            <p className="text-sm animate-pulse">Eden is thinking...</p>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit} className="border-t p-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ask Eden a question..."
            className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={sendMessage.isPending}
          />
          <button
            type="submit"
            disabled={sendMessage.isPending || !message.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
          >
            Send
          </button>
        </div>
      </form>
    </div>
  );
}
