"use client";

import { useState, useEffect } from "react";
import { api } from "@/utils/trpc-provider";

interface MicroservicePayload {
  statement?: string;
  answer?: string;
  steps?: string[];
}

interface SessionMetadata {
  [key: string]: unknown;
}

interface Problem {
  id: string;
  title: string;
  statement: string;
  microservice_payload: MicroservicePayload;
}

interface Session {
  id: string;
  user_id: string;
  status: string;
  session_metadata: SessionMetadata;
}

interface ChatMessage {
  id: string;
  session_id: string;
  sender_type: string;
  content: string;
  timestamp: string;
}

export default function MicroserviceDemo() {
  const [selectedProblem, setSelectedProblem] = useState<Problem | null>(null);
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [chatMessage, setChatMessage] = useState("");
  const [socraticMessage, setSocraticMessage] = useState("");
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [streamingMessage, setStreamingMessage] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingStatus, setStreamingStatus] = useState("");

  // API calls
  const { data: problems, isLoading: problemsLoading } =
    api.chat.getProblems.useQuery();

  const createSessionMutation = api.chat.getOrCreateSession.useMutation({
    onSuccess: (session) => {
      setCurrentSession(session);
      refetchChatHistory();
    },
  });

  const sendChatMutation = api.chat.sendChatMessage.useMutation({
    onSuccess: async (data) => {
      console.log("Chat message sent successfully:", data);
      setChatMessage("");
      // Add a small delay to ensure database consistency before refetching
      // setTimeout(() => {
      //   refetchChatHistory();
      // }, 500);
    },
    onError: (error) => {
      console.error("Error sending chat message:", error);
    },
  });

  const sendSocraticMutation = api.chat.sendSocraticMessage.useMutation({
    onSuccess: async (data) => {
      console.log("Socratic message sent successfully:", data);
      setSocraticMessage("");
      // Add a small delay to ensure database consistency before refetching
      // setTimeout(() => {
      //   refetchChatHistory();
      // }, 500);
    },
    onError: (error) => {
      console.error("Error sending socratic message:", error);
    },
  });

  const { data: chatHistoryData, refetch: refetchChatHistory } =
    api.chat.getChatHistory.useQuery(
      {
        sessionId: currentSession?.id || "",
        problemId: selectedProblem?.id,
      },
      { enabled: !!currentSession?.id }
    );

  useEffect(() => {
    if (chatHistoryData) {
      console.log("Chat history updated:", chatHistoryData);
      setChatHistory(chatHistoryData);
    }
  }, [chatHistoryData]);

  const handleProblemSelect = (problem: Problem) => {
    setSelectedProblem(problem);
    createSessionMutation.mutate({ problemId: problem.id });
  };

  const handleSendChat = () => {
    if (!currentSession || !selectedProblem || !chatMessage.trim()) return;

    // Store the user message for optimistic UI update
    const userMessage = chatMessage;

    // Add user message to chat history optimistically
    const tempUserMessage: ChatMessage = {
      id: `temp-user-${Date.now()}`,
      session_id: currentSession.id,
      sender_type: "student",
      content: userMessage,
      timestamp: new Date().toISOString(),
    };
    setChatHistory((prev) => [...prev, tempUserMessage]);

    sendChatMutation.mutate({
      sessionId: currentSession.id,
      problemId: selectedProblem.id,
      userMessage: userMessage,
    });
  };

  const handleSendSocratic = () => {
    if (!currentSession || !selectedProblem || !socraticMessage.trim()) return;

    // Store the user message for optimistic UI update
    const userMessage = socraticMessage;

    // Add user message to chat history optimistically
    const tempUserMessage: ChatMessage = {
      id: `temp-socratic-${Date.now()}`,
      session_id: currentSession.id,
      sender_type: "student",
      content: userMessage,
      timestamp: new Date().toISOString(),
    };
    setChatHistory((prev) => [...prev, tempUserMessage]);

    sendSocraticMutation.mutate({
      sessionId: currentSession.id,
      problemId: selectedProblem.id,
      studentExplanation: userMessage,
      stepsCompleted: [],
    });
  };

  const simulateStreaming = async (text: string) => {
    const words = text.split(" ");
    let currentText = "";

    for (let i = 0; i < words.length; i++) {
      currentText += (i > 0 ? " " : "") + words[i];
      setStreamingMessage(currentText);
      await new Promise((resolve) => setTimeout(resolve, 100)); // Delay between words
    }
  };

  const handleSendStreamingChat = async () => {
    if (
      !currentSession ||
      !selectedProblem ||
      !chatMessage.trim() ||
      isStreaming
    )
      return;

    setIsStreaming(true);
    setStreamingMessage("");
    setStreamingStatus("Processing your message...");

    try {
      // Store the user message immediately for optimistic UI update
      const userMessage = chatMessage;
      setChatMessage(""); // Clear input immediately

      // Add user message to chat history optimistically
      const tempUserMessage: ChatMessage = {
        id: `temp-user-${Date.now()}`,
        session_id: currentSession.id,
        sender_type: "student",
        content: userMessage,
        timestamp: new Date().toISOString(),
      };
      setChatHistory((prev) => [...prev, tempUserMessage]);

      // Call the regular mutation but handle the response for streaming
      const result = await sendChatMutation.mutateAsync({
        sessionId: currentSession.id,
        problemId: selectedProblem.id,
        userMessage: userMessage,
      });

      setStreamingStatus("AI is responding...");

      // Simulate streaming the response
      await simulateStreaming(result.reply);

      // Complete the streaming - the AI message is already stored by the mutation
      setIsStreaming(false);
      setStreamingStatus("");
      setStreamingMessage("");

      // Refetch to get the actual persisted messages and replace optimistic updates
      setTimeout(() => {
        refetchChatHistory();
      }, 500);
    } catch (error) {
      console.error("Streaming error:", error);
      setIsStreaming(false);
      setStreamingStatus("Error occurred");
      setStreamingMessage("");
      // Refetch to ensure consistency even on error
      setTimeout(() => {
        refetchChatHistory();
      }, 500);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Microservice Demo</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Problem Selection */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Select a Problem</h2>

          {problemsLoading ? (
            <div>Loading problems...</div>
          ) : (
            <div className="space-y-3">
              {problems?.map((problem) => (
                <div
                  key={problem.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedProblem?.id === problem.id
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                  onClick={() => handleProblemSelect(problem)}
                >
                  <h3 className="font-medium">{problem.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {problem.statement}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Problem Details */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Problem Details</h2>

          {selectedProblem ? (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">Title:</h3>
                <p>{selectedProblem.title}</p>
              </div>

              <div>
                <h3 className="font-medium">Problem Statement:</h3>
                <p className="text-sm bg-gray-50 p-3 rounded">
                  {selectedProblem.microservice_payload?.statement ||
                    "No statement available"}
                </p>
              </div>

              <div>
                <h3 className="font-medium">Expected Answer:</h3>
                <p className="text-sm">
                  {selectedProblem.microservice_payload?.answer ||
                    "No answer available"}
                </p>
              </div>

              <div>
                <h3 className="font-medium">Steps:</h3>
                <ol className="text-sm list-decimal list-inside space-y-1">
                  {selectedProblem.microservice_payload?.steps?.map(
                    (step: string, index: number) => <li key={index}>{step}</li>
                  ) || <li>No steps available</li>}
                </ol>
              </div>

              {currentSession && (
                <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                  <p className="text-sm text-green-700">
                    Session Active: {currentSession.id}
                  </p>
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">Select a problem to see details</p>
          )}
        </div>
      </div>

      {/* Chat Interface */}
      {currentSession && selectedProblem && (
        <div className="mt-6 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Chat Interface</h2>

          {/* Chat History */}
          <div className="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto mb-4">
            {chatHistory.length === 0 ? (
              <p className="text-gray-500">
                No messages yet. Start a conversation!
              </p>
            ) : (
              <div className="space-y-3">
                {chatHistory.map((message) => (
                  <div
                    key={message.id}
                    className={`p-3 rounded-lg max-w-xs ${
                      message.sender_type === "student"
                        ? "bg-blue-100 ml-auto"
                        : "bg-white border"
                    }`}
                  >
                    <div className="text-xs text-gray-500 mb-1">
                      {message.sender_type === "student" ? "You" : "AI"}
                    </div>
                    <div className="text-sm">{message.content}</div>
                  </div>
                ))}

                {/* Show streaming message */}
                {isStreaming && (
                  <div className="p-3 rounded-lg max-w-xs bg-white border">
                    <div className="text-xs text-gray-500 mb-1">AI</div>
                    <div className="text-sm">
                      {streamingMessage || streamingStatus}
                      {streamingMessage && (
                        <span className="animate-pulse">|</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Chat Input */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Chat Message (General Help)</h3>
              <div className="flex gap-2 mb-2">
                <input
                  type="text"
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  placeholder="Ask for help with the problem..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onKeyDown={(e) => e.key === "Enter" && handleSendChat()}
                  disabled={isStreaming}
                />
                <button
                  onClick={handleSendChat}
                  disabled={
                    sendChatMutation.isPending ||
                    !chatMessage.trim() ||
                    isStreaming
                  }
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
                >
                  {sendChatMutation.isPending ? "..." : "Send"}
                </button>
              </div>

              {/* Streaming option */}
              <div className="flex gap-2">
                <button
                  onClick={handleSendStreamingChat}
                  disabled={isStreaming || !chatMessage.trim()}
                  className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50 text-sm"
                >
                  {isStreaming ? "Streaming..." : "Send with Streaming"}
                </button>
                {isStreaming && (
                  <div className="flex items-center text-sm text-gray-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500 mr-2"></div>
                    {streamingStatus}
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-2">
                Socratic Message (Step Explanation)
              </h3>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={socraticMessage}
                  onChange={(e) => setSocraticMessage(e.target.value)}
                  placeholder="Explain your approach to solving this..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  onKeyDown={(e) => e.key === "Enter" && handleSendSocratic()}
                />
                <button
                  onClick={handleSendSocratic}
                  disabled={
                    sendSocraticMutation.isPending || !socraticMessage.trim()
                  }
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
                >
                  {sendSocraticMutation.isPending ? "..." : "Send"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
