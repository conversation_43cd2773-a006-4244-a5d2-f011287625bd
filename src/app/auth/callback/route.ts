import { createClient } from "@/utils/supabase/server";
import { createServerClient } from "@supabase/ssr";
import { NextRequest, NextResponse } from "next/server";
import { getDefaultRedirectPath, type UserRole } from "@/lib/auth/auth-utils";

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get("code");
  const error = searchParams.get("error");
  const errorDescription = searchParams.get("error_description");

  console.log("Auth callback route triggered with:", {
    code: code ? "present" : "missing",
    error,
    errorDescription,
  });

  // Handle error from auth provider
  if (error) {
    console.error("Auth provider error:", error, errorDescription);
    return NextResponse.redirect(`${origin}/auth/magic-link?error=${error}`);
  }

  if (!code) {
    console.error("No code provided");
    return NextResponse.redirect(`${origin}/auth/magic-link?error=no_code`);
  }

  const supabase = await createClient();

  try {
    // We'll determine the redirect URL later based on user role
    let redirectUrl = `${origin}/student/dashboard`;

    // Create a temporary response to capture cookies
    const tempResponse = NextResponse.redirect(redirectUrl);

    // Create a new Supabase client that can set cookies in the response
    const supabaseWithResponse = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        auth: {
          flowType: "pkce",
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: true,
        },
        cookies: {
          getAll() {
            return request.cookies.getAll().map((cookie) => ({
              name: cookie.name,
              value: cookie.value,
            }));
          },
          setAll(cookiesToSet: any) {
            cookiesToSet.forEach(({ name, value, options }: any) => {
              tempResponse.cookies.set(name, value, options);
            });
          },
        },
      }
    );

    // Exchange the code for a session
    const { data, error: exchangeError } =
      await supabaseWithResponse.auth.exchangeCodeForSession(code);

    if (exchangeError) {
      console.error("Exchange error:", exchangeError);
      return NextResponse.redirect(
        `${origin}/auth/magic-link?error=exchange_failed`
      );
    }

    if (!data.session || !data.session.user) {
      console.error("No session or user found");
      return NextResponse.redirect(
        `${origin}/auth/magic-link?error=no_session`
      );
    }

    const user = data.session.user;

    // Check if user profile exists
    const { data: existingProfile, error: profileError } = await supabase
      .from("users")
      .select("id, role")
      .eq("id", user.id)
      .maybeSingle();

    let userRole = existingProfile?.role || "student";

    // Create profile if it doesn't exist
    if (!existingProfile) {
      const { error: insertError } = await supabase.from("users").insert({
        id: user.id,
        email: user.email || "",
        role: "student", // Default role for new users
        full_name: user.email?.split("@")[0] || "Student",
      });

      if (insertError) {
        console.error("Error creating user profile:", insertError);
        // If we can't create a profile, sign out the user and redirect to home
        await supabaseWithResponse.auth.signOut();
        return NextResponse.redirect(
          `${origin}/?error=profile_creation_failed`
        );
      } else {
        userRole = "student";
      }
    } else if (profileError) {
      console.error("Error fetching user profile:", profileError);
      // If there's a database error, sign out and redirect to home
      await supabaseWithResponse.auth.signOut();
      return NextResponse.redirect(`${origin}/?error=database_error`);
    }

    // Role-based redirection logic
    if (userRole === "student") {
      // Check if student has completed intake
      const { data: studentProfile } = await supabase
        .from("student_profiles")
        .select("name, grade")
        .eq("user_id", user.id)
        .maybeSingle();

      if (!studentProfile || !studentProfile.name || !studentProfile.grade) {
        redirectUrl = `${origin}/student/intake/start`;
      } else {
        redirectUrl = `${origin}/student/dashboard`;
      }
    } else {
      // For teachers and admins, redirect to their respective dashboards
      const redirectPath = getDefaultRedirectPath(userRole as UserRole);
      redirectUrl = `${origin}${redirectPath}`;
    }

    // Create final response with the correct redirect URL and cookies
    const finalResponse = NextResponse.redirect(redirectUrl);

    // Copy cookies from tempResponse to finalResponse
    tempResponse.cookies.getAll().forEach((cookie) => {
      finalResponse.cookies.set(cookie.name, cookie.value);
    });

    return finalResponse;
  } catch (error) {
    console.error("Auth callback error:", error);
    return NextResponse.redirect(
      `${origin}/auth/magic-link?error=callback_error`
    );
  }
}
