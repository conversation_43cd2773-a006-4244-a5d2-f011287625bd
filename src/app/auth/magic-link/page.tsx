"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import Image from "next/image";
import edengagelogo from "../../../../public/images/edengage-logo.png";

export default function MagicLinkAuthPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");
  const {
    user,
    userRole,
    isLoading: authLoading,
    signInWithMagicLink,
  } = useAuth();
  const router = useRouter();

  // Show a message if user is already authenticated
  const isAuthenticated = !authLoading && user && userRole;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");
    if (!email || !email.includes("@")) {
      setError("Please enter a valid email address");
      return;
    }
    setIsLoading(true);
    try {
      const { error } = await signInWithMagicLink(email);
      if (error) throw error;
      setMessage(
        `If we found or created your account, we've sent a magic link to ${email}. Please check your inbox!`
      );
    } catch (err: any) {
      setError(err.message || "Failed to send magic link");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
        <div className="w-full max-w-md">
          <div className="rounded-lg bg-white p-8 shadow-md text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking authentication...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show message for authenticated users (middleware should handle redirect)
  if (isAuthenticated) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
        <div className="w-full max-w-md">
          <div className="rounded-lg bg-white p-8 shadow-md text-center">
            <div className="mb-4">
              <svg
                className="mx-auto h-12 w-12 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Already Logged In
            </h2>
            <p className="text-gray-600 mb-4">
              You're already authenticated as {user?.email}
            </p>
            <div className="space-y-2">
              <a
                href="/student/dashboard"
                className="block w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Go to Dashboard
              </a>
              <button
                onClick={() => router.back()}
                className="block w-full bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300 transition-colors"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-md">
        <div className="mb-6 flex justify-center">
          <button className="logoButton">EdEngage</button>
        </div>
        <div className="rounded-lg bg-white p-8 shadow-md">
          <h2 className="mb-6 text-center text-2xl font-bold">Welcome!</h2>
          <p className="mb-6 text-center text-gray-600">
            Enter your email to log in or create your account. You’ll receive a
            secure sign-in link!
          </p>

          {error && (
            <div className="mb-4 rounded-md bg-red-50 p-4 text-red-500 text-center">
              {error}
            </div>
          )}
          {message && (
            <div className="mb-4 rounded-md bg-green-50 p-4 text-green-600 text-center">
              {message}
            </div>
          )}

          {!message && (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="mt-1 block w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full rounded-md bg-blue-600 py-3 text-white hover:bg-blue-700 disabled:bg-blue-300 transition-colors"
              >
                {isLoading ? "Sending Link..." : "Send Magic Link"}
              </button>
            </form>
          )}
          {message && (
            <div className="mt-6 flex flex-col items-center">
              <svg
                className="mb-2"
                width={40}
                height={40}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <polyline
                  points="20 6 9 17 4 12"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span>
                Didn't get an email? Check your spam folder or try again.
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
