"use server";

import { createClient } from "@/utils/supabase/server";
// import { cookies } from "next/headers"; // No longer needed

export async function voteOnAIResponse(id: string, decision: boolean | null, description: string | null) {
  // const cookieStore = cookies(); // No longer needed
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Error fetching user or no user logged in:", authError);
    return { success: false, data: null, error: "User not authenticated" };
  }

  try {
    const { error } = await supabase
      .from("chat_messages")
      .update({student_vote: decision, downvote_description: description})
      .eq('id', id)
    
    if (error) {
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (e: unknown) {
    console.error("Unexpected error voting on chat response", e);
    const errorMessage =
      e instanceof Error ? e.message : "An unexpected error occurred";
    return { success: false, data: null, error: errorMessage };
  }
}
