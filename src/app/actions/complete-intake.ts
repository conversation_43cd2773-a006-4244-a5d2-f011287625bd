"use server";

import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { z } from "zod";
import { revalidatePath } from "next/cache";

// Validation schema matching your intake form
const intakeFormSchema = z.object({
  // Basic Info
  name: z.string().min(2, "Name must be at least 2 characters").max(100),
  grade: z.enum(["6th", "7th", "8th", "9th"], {
    errorMap: () => ({ message: "Please select a valid grade" }),
  }),
  age: z.number().int("Please select a valid integer").gte(10).lte(25),
  languages: z.array(z.string()).min(1, "Please select a language"),

  // Learning preferences
  learning_style: z.enum([
    "visual",
    "auditory",
    "kinesthetic",
    "reading_writing",
  ]),
  challenge_preference: z.enum(["easy", "moderate", "difficult", "applied"]),
  attention_span: z.enum(["short", "medium", "long"]),

  // Math confidence
  math_confidence: z.enum([
    "not_confident",
    "somewhat_unconfident",
    "neutral",
    "somewhat_confident",
    "very_confident",
  ]),

  // Arrays
  challenging_topics: z.array(z.string()).optional().default([]),
  preferred_explanation_methods: z.array(z.string()).optional().default([]),
  interests: z.array(z.string()).optional().default([]),
});

export type IntakeFormData = z.infer<typeof intakeFormSchema>;

export interface IntakeResult {
  success: boolean;
  error?: string;
  data?: {
    userId: string;
    profileCreated: boolean;
  };
}

export async function completeIntakeForm(
  formData: IntakeFormData
): Promise<IntakeResult> {
  try {
    // 1. Server-side validation
    const validatedData = intakeFormSchema.parse(formData);

    // 2. Get authenticated user with proper cookie handling
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required. Please log in again.",
      };
    }

    // 3. Begin database transaction (using Supabase RPC for atomicity)
    const rpcParams = {
      p_user_id: user.id,
      p_email: user.email || "",
      p_name: validatedData.name,
      p_grade: validatedData.grade,
      p_age: validatedData.age,
      p_languages: validatedData.languages,
      p_learning_style: validatedData.learning_style,
      p_challenge_preference: validatedData.challenge_preference,
      p_attention_span: validatedData.attention_span,
      p_math_confidence: validatedData.math_confidence,
      p_challenging_topics: validatedData.challenging_topics,
      p_explanation_methods: validatedData.preferred_explanation_methods,
      p_interests: validatedData.interests,
    };

    const { error: transactionError } = await supabase.rpc(
      "complete_student_intake",
      rpcParams
    );

    if (transactionError) {
      console.error("Database transaction error:", transactionError);
      return {
        success: false,
        error: "Failed to save your profile. Please try again.",
      };
    }

    // 4. Revalidate relevant paths
    revalidatePath("/student/dashboard");
    revalidatePath("/student/intake");

    // 5. Success response
    return {
      success: true,
      data: {
        userId: user.id,
        profileCreated: true,
      },
    };
  } catch (error) {
    console.error("Intake form completion error:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: `Validation error: ${
          error.errors[0]?.message || "Invalid form data"
        }`,
      };
    }

    return {
      success: false,
      error: "An unexpected error occurred. Please try again.",
    };
  }
}
