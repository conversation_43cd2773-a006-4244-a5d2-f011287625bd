"use server";

import { createClient } from "@/utils/supabase/server";
// import { cookies } from "next/headers"; // No longer needed

export async function getStudentProfileData() {
  // const cookieStore = cookies(); // No longer needed
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Error fetching user or no user logged in:", authError);
    return { success: false, data: null, error: "User not authenticated" };
  }

  try {
    const { data, error } = await supabase
      .from("student_profiles")
      .select("name, grade, modality_preference, attention_span")
      .eq("user_id", user.id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        console.warn(
          "Student profile not found for user_id (PGRST116):",
          user.id,
          error.message
        );
        return { success: false, data: null, error: "Profile not found" };
      }
      console.error("Error fetching student profile:", error);
      return { success: false, data: null, error: error.message };
    }

    if (data) {
      const profileData = {
        name: data.name || null,
        grade: data.grade || null,
        modality_preference: data.modality_preference || null,
        attention_span: data.attention_span || null,
      };
      return { success: true, data: profileData, error: null };
    } else {
      console.warn(
        "Student profile not found (no data returned) for user_id:",
        user.id
      );
      return { success: false, data: null, error: "Profile not found" };
    }
  } catch (e: unknown) {
    console.error("Unexpected error fetching student profile:", e);
    const errorMessage =
      e instanceof Error ? e.message : "An unexpected error occurred";
    return { success: false, data: null, error: errorMessage };
  }
}
