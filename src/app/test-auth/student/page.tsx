// src/app/test-auth/student/page.tsx
import { requireStudent } from "@/hooks/use-server-auth";

export default async function TestStudentPage() {
  // This will redirect if user is not authenticated or doesn't have student+ role
  const user = await requireStudent();

  return (
    <div className="min-h-screen bg-green-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-green-800 mb-4">
            ✅ Student Page Access Test
          </h1>
          <p className="text-gray-700 mb-4">
            If you can see this page, the student role protection is working correctly!
          </p>
          <div className="bg-green-100 p-4 rounded">
            <h2 className="font-semibold text-green-800">User Information:</h2>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Role:</strong> {user.role}</p>
            <p><strong>User ID:</strong> {user.id}</p>
          </div>
          <div className="mt-6">
            <a 
              href="/test-auth" 
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Back to Test Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
