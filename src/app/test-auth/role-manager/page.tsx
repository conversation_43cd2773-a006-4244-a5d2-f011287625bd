// src/app/test-auth/role-manager/page.tsx
"use client";

import { useState } from "react";
import { supabase } from "@/utils/supabase/client";
import { useAuth } from "@/contexts/auth-context";

export default function RoleManagerPage() {
  const { user, userRole, refreshAuth } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);
  const [message, setMessage] = useState("");

  const updateRole = async (newRole: "student" | "teacher" | "admin") => {
    if (!user) {
      setMessage("❌ No user logged in");
      return;
    }

    setIsUpdating(true);
    setMessage("⏳ Updating role...");

    try {
      const { error } = await supabase
        .from("users")
        .update({ role: newRole })
        .eq("id", user.id);

      if (error) {
        setMessage(`❌ Error updating role: ${error.message}`);
      } else {
        setMessage(`✅ Role updated to ${newRole}! Refreshing auth...`);
        // Refresh auth state to get new role
        await refreshAuth();
        setMessage(`✅ Role successfully updated to ${newRole}!`);
      }
    } catch (error: any) {
      setMessage(`❌ Error: ${error.message}`);
    } finally {
      setIsUpdating(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-2xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Role Manager</h1>
            <p className="text-red-600">Please log in to use the role manager.</p>
            <a 
              href="/auth/magic-link" 
              className="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 inline-block"
            >
              Go to Login
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Role Manager</h1>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded p-4 mb-6">
            <h2 className="font-semibold text-yellow-800">⚠️ Testing Tool</h2>
            <p className="text-yellow-700 text-sm">
              This tool is for testing purposes only. It allows you to change your role 
              to test different access levels. In a production app, only admins would 
              be able to change user roles.
            </p>
          </div>

          {/* Current User Info */}
          <div className="bg-gray-50 p-4 rounded mb-6">
            <h2 className="font-semibold text-gray-800 mb-2">Current User</h2>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Current Role:</strong> {userRole || "No role"}</p>
            <p><strong>User ID:</strong> {user.id}</p>
          </div>

          {/* Role Update Buttons */}
          <div className="mb-6">
            <h2 className="font-semibold text-gray-800 mb-4">Change Role</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => updateRole("student")}
                disabled={isUpdating || userRole === "student"}
                className={`px-4 py-2 rounded font-medium ${
                  userRole === "student"
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-green-500 text-white hover:bg-green-600"
                } ${isUpdating ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                Set as Student
              </button>
              <button
                onClick={() => updateRole("teacher")}
                disabled={isUpdating || userRole === "teacher"}
                className={`px-4 py-2 rounded font-medium ${
                  userRole === "teacher"
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-blue-500 text-white hover:bg-blue-600"
                } ${isUpdating ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                Set as Teacher
              </button>
              <button
                onClick={() => updateRole("admin")}
                disabled={isUpdating || userRole === "admin"}
                className={`px-4 py-2 rounded font-medium ${
                  userRole === "admin"
                    ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                    : "bg-purple-500 text-white hover:bg-purple-600"
                } ${isUpdating ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                Set as Admin
              </button>
            </div>
          </div>

          {/* Status Message */}
          {message && (
            <div className="bg-gray-50 p-4 rounded mb-6">
              <p className="font-mono text-sm">{message}</p>
            </div>
          )}

          {/* Navigation */}
          <div className="space-x-4">
            <a 
              href="/test-auth" 
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 inline-block"
            >
              Back to Test Dashboard
            </a>
            <a 
              href="/test-auth/trpc" 
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 inline-block"
            >
              Test tRPC Endpoints
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
