// src/app/test-auth/teacher/page.tsx
import { requireTeacher } from "@/hooks/use-server-auth";

export default async function TestTeacherPage() {
  // This will redirect if user is not authenticated or doesn't have teacher+ role
  const user = await requireTeacher();

  return (
    <div className="min-h-screen bg-blue-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-blue-800 mb-4">
            ✅ Teacher Page Access Test
          </h1>
          <p className="text-gray-700 mb-4">
            If you can see this page, the teacher role protection is working correctly!
            Only teachers and admins should be able to access this page.
          </p>
          <div className="bg-blue-100 p-4 rounded">
            <h2 className="font-semibold text-blue-800">User Information:</h2>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Role:</strong> {user.role}</p>
            <p><strong>User ID:</strong> {user.id}</p>
          </div>
          <div className="mt-6">
            <a 
              href="/test-auth" 
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Back to Test Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
