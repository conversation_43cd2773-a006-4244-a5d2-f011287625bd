// src/app/test-auth/page.tsx
"use client";

import { useAuth } from "@/contexts/auth-context";
import { useState } from "react";
import Link from "next/link";

export default function TestAuthPage() {
  const { user, userRole, isLoading, refreshAuth } = useAuth();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${result}`,
    ]);
  };

  const testClientAuth = () => {
    addResult(`User: ${user ? user.email : "null"}`);
    addResult(`User Role: ${userRole || "null"}`);
    addResult(`Is Loading: ${isLoading}`);
  };

  const testRefreshAuth = async () => {
    addResult("Testing refresh auth...");
    await refreshAuth();
    addResult("Refresh auth completed");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4">Loading authentication state...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Authentication Testing Dashboard
        </h1>

        {/* Current Auth State */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">
            Current Authentication State
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium text-gray-700">User</h3>
              <p className="text-sm">
                {user ? user.email : "Not authenticated"}
              </p>
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium text-gray-700">Role</h3>
              <p className="text-sm">{userRole || "No role"}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-medium text-gray-700">Loading</h3>
              <p className="text-sm">{isLoading ? "Yes" : "No"}</p>
            </div>
          </div>
        </div>

        {/* Test Actions */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="space-y-4">
            <button
              onClick={testClientAuth}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test Client Auth State
            </button>
            <button
              onClick={testRefreshAuth}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 ml-4"
            >
              Test Refresh Auth
            </button>
          </div>
        </div>

        {/* Route Testing Links */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Route Access Testing</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Student Routes</h3>
              <div className="space-y-2">
                <Link
                  href="/student/dashboard"
                  className="block text-blue-600 hover:underline"
                >
                  Student Dashboard
                </Link>
                <Link
                  href="/test-auth/student"
                  className="block text-blue-600 hover:underline"
                >
                  Test Student Page
                </Link>
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Teacher Routes</h3>
              <div className="space-y-2">
                <Link
                  href="/test-auth/teacher"
                  className="block text-blue-600 hover:underline"
                >
                  Test Teacher Page
                </Link>
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Admin Routes</h3>
              <div className="space-y-2">
                <Link
                  href="/test-auth/admin"
                  className="block text-blue-600 hover:underline"
                >
                  Test Admin Page
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Testing Tools */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">
            Additional Testing Tools
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link
              href="/test-auth/trpc"
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-center block"
            >
              Test tRPC Endpoints
            </Link>
            <Link
              href="/test-auth/role-manager"
              className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 text-center block"
            >
              Role Manager (Change Your Role)
            </Link>
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="bg-gray-50 p-4 rounded max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">
                No test results yet. Click the test buttons above.
              </p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
          <button
            onClick={() => setTestResults([])}
            className="mt-4 bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            Clear Results
          </button>
        </div>
      </div>
    </div>
  );
}
