// src/app/test-auth/trpc/page.tsx
"use client";

import { api } from "@/utils/trpc-provider";
import { useState } from "react";

export default function TestTRPCPage() {
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // tRPC queries
  const publicQuery = api.testAuth.public.useQuery();
  const protectedQuery = api.testAuth.protected.useQuery();
  const studentQuery = api.testAuth.student.useQuery();
  const teacherQuery = api.testAuth.teacher.useQuery();
  const adminQuery = api.testAuth.admin.useQuery();
  const currentUserQuery = api.testAuth.getCurrentUser.useQuery();

  // tRPC mutation
  const updateUserRoleMutation = api.testAuth.updateUserRole.useMutation();

  const testPublicEndpoint = () => {
    if (publicQuery.data) {
      addResult(`✅ Public: ${publicQuery.data.message}`);
    } else if (publicQuery.error) {
      addResult(`❌ Public: ${publicQuery.error.message}`);
    } else {
      addResult("⏳ Public: Loading...");
    }
  };

  const testProtectedEndpoint = () => {
    if (protectedQuery.data) {
      addResult(`✅ Protected: ${protectedQuery.data.message}`);
    } else if (protectedQuery.error) {
      addResult(`❌ Protected: ${protectedQuery.error.message}`);
    } else {
      addResult("⏳ Protected: Loading...");
    }
  };

  const testStudentEndpoint = () => {
    if (studentQuery.data) {
      addResult(`✅ Student: ${studentQuery.data.message}`);
    } else if (studentQuery.error) {
      addResult(`❌ Student: ${studentQuery.error.message}`);
    } else {
      addResult("⏳ Student: Loading...");
    }
  };

  const testTeacherEndpoint = () => {
    if (teacherQuery.data) {
      addResult(`✅ Teacher: ${teacherQuery.data.message}`);
    } else if (teacherQuery.error) {
      addResult(`❌ Teacher: ${teacherQuery.error.message}`);
    } else {
      addResult("⏳ Teacher: Loading...");
    }
  };

  const testAdminEndpoint = () => {
    if (adminQuery.data) {
      addResult(`✅ Admin: ${adminQuery.data.message}`);
    } else if (adminQuery.error) {
      addResult(`❌ Admin: ${adminQuery.error.message}`);
    } else {
      addResult("⏳ Admin: Loading...");
    }
  };

  const testAdminMutation = async () => {
    try {
      const result = await updateUserRoleMutation.mutateAsync({
        userId: "test-user-id",
        newRole: "teacher",
      });
      addResult(`✅ Admin Mutation: ${result.message}`);
    } catch (error: any) {
      addResult(`❌ Admin Mutation: ${error.message}`);
    }
  };

  const testCurrentUser = () => {
    if (currentUserQuery.data) {
      addResult(`✅ Current User: ${currentUserQuery.data.email} (${currentUserQuery.data.role})`);
    } else if (currentUserQuery.error) {
      addResult(`❌ Current User: ${currentUserQuery.error.message}`);
    } else {
      addResult("⏳ Current User: Loading...");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">tRPC Authentication Testing</h1>
        
        {/* Current Data Display */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Query States</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-gray-50 p-3 rounded">
              <h3 className="font-medium">Public Query</h3>
              <p className={publicQuery.isLoading ? "text-yellow-600" : publicQuery.error ? "text-red-600" : "text-green-600"}>
                {publicQuery.isLoading ? "Loading..." : publicQuery.error ? "Error" : "Success"}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <h3 className="font-medium">Protected Query</h3>
              <p className={protectedQuery.isLoading ? "text-yellow-600" : protectedQuery.error ? "text-red-600" : "text-green-600"}>
                {protectedQuery.isLoading ? "Loading..." : protectedQuery.error ? "Error" : "Success"}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <h3 className="font-medium">Student Query</h3>
              <p className={studentQuery.isLoading ? "text-yellow-600" : studentQuery.error ? "text-red-600" : "text-green-600"}>
                {studentQuery.isLoading ? "Loading..." : studentQuery.error ? "Error" : "Success"}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <h3 className="font-medium">Teacher Query</h3>
              <p className={teacherQuery.isLoading ? "text-yellow-600" : teacherQuery.error ? "text-red-600" : "text-green-600"}>
                {teacherQuery.isLoading ? "Loading..." : teacherQuery.error ? "Error" : "Success"}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <h3 className="font-medium">Admin Query</h3>
              <p className={adminQuery.isLoading ? "text-yellow-600" : adminQuery.error ? "text-red-600" : "text-green-600"}>
                {adminQuery.isLoading ? "Loading..." : adminQuery.error ? "Error" : "Success"}
              </p>
            </div>
            <div className="bg-gray-50 p-3 rounded">
              <h3 className="font-medium">Current User Query</h3>
              <p className={currentUserQuery.isLoading ? "text-yellow-600" : currentUserQuery.error ? "text-red-600" : "text-green-600"}>
                {currentUserQuery.isLoading ? "Loading..." : currentUserQuery.error ? "Error" : "Success"}
              </p>
            </div>
          </div>
        </div>

        {/* Test Buttons */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test tRPC Endpoints</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={testPublicEndpoint}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Test Public
            </button>
            <button
              onClick={testProtectedEndpoint}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Test Protected
            </button>
            <button
              onClick={testStudentEndpoint}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Test Student
            </button>
            <button
              onClick={testTeacherEndpoint}
              className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
            >
              Test Teacher
            </button>
            <button
              onClick={testAdminEndpoint}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Test Admin
            </button>
            <button
              onClick={testAdminMutation}
              className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
            >
              Test Admin Mutation
            </button>
            <button
              onClick={testCurrentUser}
              className="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600"
            >
              Test Current User
            </button>
          </div>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="bg-gray-50 p-4 rounded max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet. Click the test buttons above.</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="mt-4 space-x-4">
            <button
              onClick={() => setTestResults([])}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Clear Results
            </button>
            <a 
              href="/test-auth" 
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 inline-block"
            >
              Back to Test Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
