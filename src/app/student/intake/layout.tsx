// src/app/student/intake/layout.tsx
"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";
import ProtectedRoute from "@/components/protected-route";

interface StepIndicatorProps {
  step: string;
  isCompleted: boolean;
  isActive: boolean;
}

function StepIndicator({ step, isCompleted, isActive }: StepIndicatorProps) {
  return (
    <div className="relative mb-12 flex items-center">
      <div
        className={`
          z-10 flex h-10 w-10 items-center justify-center rounded-full border-2 
          ${
            isCompleted
              ? "border-blue-500 bg-blue-500 text-white"
              : isActive
              ? "border-blue-500 bg-white text-blue-900"
              : "border-blue-700 bg-transparent text-white"
          }
        `}
      >
        {isCompleted ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        ) : null}
      </div>
      <span
        className={`ml-4 text-lg ${
          isActive ? "font-bold text-white" : "text-blue-300"
        }`}
      >
        {step}
      </span>
    </div>
  );
}

export default function IntakeLayout({ children }: { children: ReactNode }) {
  const pathname = usePathname();

  // Skip protection for the start page
  const isStartPage = pathname === "/student/intake/start";

  // Determine current step from URL
  const getCurrentStep = () => {
    if (pathname.includes("/basic-info")) return "basic-info";
    if (pathname.includes("/learning-style")) return "learning-style";
    if (pathname.includes("/math-confidence")) return "math-confidence";
    if (pathname.includes("/learning-preferences"))
      return "learning-preferences";
    return "";
  };

  const currentStep = getCurrentStep();

  // Determine completed steps based on current step
  const isCompleted = (step: string) => {
    const steps = [
      "basic-info",
      "learning-style",
      "math-confidence",
      "learning-preferences",
    ];
    const currentIndex = steps.indexOf(currentStep);
    const stepIndex = steps.indexOf(step);
    return stepIndex < currentIndex;
  };

  const content = (
    <div className="min-h-screen bg-gray-100 p-6">
      {/* Logo in top left, outside the main container */}
      <div className="absolute left-8 top-8">
        <Link href="/">
          <button className="logoButton">EdEngage</button>
        </Link>
      </div>

      {/* Main container with rounded corners */}
      <div className="mx-auto mt-12 max-w-6xl overflow-hidden rounded-2xl shadow-lg">
        <div className="flex">
          {/* Sidebar navigation */}
          <div className="w-1/3 bg-blue-900 p-8">
            <div className="mb-12 pt-6">
              {/* Empty space at top to balance with content area */}
            </div>

            <div className="relative">
              {/* <div className="absolute left-5 top-0 h-full w-0.5 bg-blue-700"></div> */}

              <StepIndicator
                step="Basic Information"
                isCompleted={isCompleted("basic-info")}
                isActive={currentStep === "basic-info"}
              />

              <StepIndicator
                step="Learning Style"
                isCompleted={isCompleted("learning-style")}
                isActive={currentStep === "learning-style"}
              />

              <StepIndicator
                step="Math Confidence"
                isCompleted={isCompleted("math-confidence")}
                isActive={currentStep === "math-confidence"}
              />

              <StepIndicator
                step="Learning Preferences"
                isCompleted={isCompleted("learning-preferences")}
                isActive={currentStep === "learning-preferences"}
              />
            </div>

            {/* Illustration at bottom */}
            <div className="mt-20">
              <Image
                src="/images/student-studying.png"
                alt="Student studying"
                width={280}
                height={280}
              />
            </div>
          </div>

          {/* Main content area */}
          <div className="w-2/3 bg-white p-8">{children}</div>
        </div>
      </div>
    </div>
  );

  // Only apply ProtectedRoute to non-start pages
  return isStartPage ? content : <ProtectedRoute>{content}</ProtectedRoute>;
}
