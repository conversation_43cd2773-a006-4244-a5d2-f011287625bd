// src/app/student/intake/basic-info/page.tsx
"use client";

import { useIntakeForm, allowedLanguages } from "@/contexts/intake-form-context";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

// Form validation schema
const basicInfoSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  grade: z.string().min(1, "Please select a grade"),
  age: z.number().int("Please select a valid integer").gte(10).lte(25),
  languages: z.array(z.string()).min(1, "Please select a language")
});

type BasicInfoFormValues = z.infer<typeof basicInfoSchema>;

export default function BasicInfoPage() {
  const router = useRouter();
  const { formData, updateMultipleFields } = useIntakeForm();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<BasicInfoFormValues>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      name: formData.name,
      grade: formData.grade,
      age: formData.age,
      languages: formData.languages,
    },
  });

  const onSubmit = (data: BasicInfoFormValues) => {
    updateMultipleFields(data);
    router.push("/student/intake/learning-style");
  };

  return (
    <div className="h-full">
      <h2 className="mb-2 text-2xl font-bold">Basic Information</h2>
      <p className="mb-8 text-gray-600">
        Let's start with some basic information
      </p>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 max-w-lg">
        <div className="space-y-2">
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700"
          >
            Your Name
          </label>
          <input
            id="name"
            type="text"
            {...register("name")}
            className="w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter your name"
          />
          {errors.name && (
            <p className="text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label
            htmlFor="grade"
            className="block text-sm font-medium text-gray-700"
          >
            Grade Level
          </label>
          <select
            id="grade"
            {...register("grade")}
            className="w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="">Select your grade</option>
            <option value="6th">6th Grade</option>
            <option value="7th">7th Grade</option>
            <option value="8th">8th Grade</option>
            <option value="9th">9th Grade</option>
          </select>
          {errors.grade && (
            <p className="text-sm text-red-500">{errors.grade.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label
            htmlFor="age"
            className="block text-sm font-medium text-gray-700"
          >
            Your Age
          </label>
          <input
            id="age" type="number"
            min="10" max="25" step="1"
            {...register("age", { valueAsNumber: true })}
            className="w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Enter your age"
          />
          {errors.age && (
            <p className="text-sm text-red-500">{errors.age.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label
            htmlFor="languages"
            className="block text-sm font-medium text-gray-700"
          >
            Preferred Languages
          </label>
          <select
            id="languages" multiple
            {...register("languages")}
            size={allowedLanguages.length}
            className="w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            {allowedLanguages.map((val: string, index: number) => 
              <option key={index} value={val}>
                {val}
              </option>
            )}
          </select>
          {errors.languages && (
            <p className="text-sm text-red-500">{errors.languages.message}</p>
          )}
        </div>

        <div className="mt-8 flex justify-end">
          <button
            type="submit"
            className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
}
