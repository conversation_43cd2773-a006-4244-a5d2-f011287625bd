// src/app/student/intake/math-confidence/page.tsx
"use client";

import { useIntakeForm } from "@/contexts/intake-form-context";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// Confidence level options
const CONFIDENCE_OPTIONS = [
  { value: "not_confident", label: "Not confident at all" },
  { value: "somewhat_unconfident", label: "Somewhat unconfident" },
  { value: "neutral", label: "Neutral" },
  { value: "somewhat_confident", label: "Somewhat confident" },
  { value: "very_confident", label: "Very confident" },
];

// Common challenging topics for middle school math
const CHALLENGING_TOPICS = [
  { value: "number_system", label: "The Number System" },
  { value: "expressions_equations", label: "Expressions & Equations" },
  { value: "geometry", label: "Geometry" },
  { value: "functions", label: "Functions" },
  { value: "statistics_probability", label: "Statistics & Probability" },
];

// Form validation schema
const mathConfidenceSchema = z.object({
  math_confidence: z.string().min(1, "Please select a confidence level"),
  challenging_topics: z.array(z.string()).optional(),
});

type MathConfidenceFormValues = z.infer<typeof mathConfidenceSchema>;

export default function MathConfidencePage() {
  const router = useRouter();
  const { formData, updateMultipleFields } = useIntakeForm();
  const [selectedTopics, setSelectedTopics] = useState<string[]>(
    formData.challenging_topics || []
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<MathConfidenceFormValues>({
    resolver: zodResolver(mathConfidenceSchema),
    defaultValues: {
      math_confidence: formData.math_confidence,
      challenging_topics: formData.challenging_topics,
    },
  });

  const handleTopicToggle = (value: string) => {
    setSelectedTopics((prev) =>
      prev.includes(value)
        ? prev.filter((topic) => topic !== value)
        : [...prev, value]
    );
  };

  const onSubmit = (data: MathConfidenceFormValues) => {
    updateMultipleFields({
      math_confidence: data.math_confidence,
      challenging_topics: selectedTopics,
    });
    router.push("/student/intake/learning-preferences");
  };

  return (
    <div className="h-full">
      <h2 className="mb-2 text-2xl font-bold">Math Confidence</h2>
      <p className="mb-8 text-gray-600">
        Let us know about your comfort level with math
      </p>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 max-w-xl">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            How confident do you feel about your math abilities?
          </label>
          <div className="space-y-2">
            {CONFIDENCE_OPTIONS.map((option) => (
              <label
                key={option.value}
                className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-300 p-3 hover:border-blue-500"
              >
                <input
                  type="radio"
                  value={option.value}
                  {...register("math_confidence")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
          {errors.math_confidence && (
            <p className="text-sm text-red-500">
              {errors.math_confidence.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Which math topics do you find most challenging? (Select all that
            apply)
          </label>
          <div className="grid grid-cols-2 gap-3">
            {CHALLENGING_TOPICS.map((topic) => (
              <label
                key={topic.value}
                className={`flex cursor-pointer items-center gap-3 rounded-lg border p-3 ${
                  selectedTopics.includes(topic.value)
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300 hover:border-blue-300"
                }`}
              >
                <input
                  type="checkbox"
                  value={topic.value}
                  checked={selectedTopics.includes(topic.value)}
                  onChange={() => handleTopicToggle(topic.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                />
                <span>{topic.label}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="mt-8 flex justify-between">
          <button
            type="button"
            onClick={() => router.push("/student/intake/learning-style")}
            className="rounded-md border border-gray-300 px-6 py-2 text-gray-700 hover:bg-gray-50"
          >
            Back
          </button>

          <button
            type="submit"
            className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
}
