// src/app/student/intake/learning-preferences/page.tsx
"use client";

import { useIntakeForm } from "@/contexts/intake-form-context";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// Explanation method options
const EXPLANATION_METHODS = [
  {
    value: "visual_diagrams",
    label: "Visual Diagrams and Graphs",
    description:
      "Using charts, graphs, and visual representations to explain concepts.",
  },
  {
    value: "step_by_step",
    label: "Step-by-Step Breakdowns",
    description:
      "Detailed, sequential explanations that show each part of the process.",
  },
  {
    value: "real_world_examples",
    label: "Real-World Examples",
    description:
      "Relating mathematical concepts to everyday situations and applications.",
  },
  {
    value: "guided_practice",
    label: "Guided Practice",
    description:
      "Working through similar problems together with guidance along the way.",
  },
];

// Challenge level options
const CHALLENGE_OPTIONS = [
  {
    value: "easy",
    label: "Easy Problems I Can Solve Quickly",
    description: "Focus on building confidence with straightforward problems.",
  },
  {
    value: "moderate",
    label: "Moderately Challenging Problems",
    description: "Problems that make me think but are within my abilities.",
  },
  {
    value: "difficult",
    label: "Very Challenging Problems",
    description: "Complex problems that really test my abilities.",
  },
  {
    value: "applied",
    label: "Problems Connected to Real-World Situations",
    description: "Math problems applied to everyday contexts and scenarios.",
  },
];

// Attention span options
const ATTENTION_SPAN_OPTIONS = [
  { value: "short", label: "Less than 15 minutes (short)" },
  { value: "medium", label: "15-30 minutes (medium)" },
  { value: "long", label: "30-60 minutes (long)" },
];

// Form validation schema
const learningPreferencesSchema = z.object({
  challenge_preference: z
    .string()
    .min(1, "Please select a challenge preference"),
  attention_span: z.string().min(1, "Please select an attention span"),
  interests: z.string().optional(),
});

type LearningPreferencesFormValues = z.infer<typeof learningPreferencesSchema>;

export default function LearningPreferencesPage() {
  const router = useRouter();
  const { formData, updateMultipleFields } = useIntakeForm();

  const [selectedMethods, setSelectedMethods] = useState<string[]>(
    formData.preferred_explanation_methods || []
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LearningPreferencesFormValues>({
    resolver: zodResolver(learningPreferencesSchema),
    defaultValues: {
      challenge_preference: formData.challenge_preference,
      attention_span: formData.attention_span,
      interests: formData.interests ? formData.interests.join(", ") : "",
    },
  });

  const handleMethodToggle = (value: string) => {
    setSelectedMethods((prev) =>
      prev.includes(value)
        ? prev.filter((method) => method !== value)
        : [...prev, value]
    );
  };

  const onSubmit = (data: LearningPreferencesFormValues) => {
    // Parse interests from comma-separated string to array
    const interestsArray = data.interests
      ? data.interests
          .split(",")
          .map((item) => item.trim())
          .filter(Boolean)
      : [];

    updateMultipleFields({
      preferred_explanation_methods: selectedMethods,
      challenge_preference: data.challenge_preference,
      attention_span: data.attention_span,
      interests: interestsArray,
    });

    router.push("/student/intake/complete");
  };

  return (
    <div className="h-full">
      <h2 className="mb-2 text-2xl font-bold">Learning Preferences</h2>
      <p className="mb-8 text-gray-600">
        Help us personalize your learning experience
      </p>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 max-w-xl">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Which explanation methods help you understand best? (Select all that
            apply)
          </label>
          <div className="grid grid-cols-2 gap-3">
            {EXPLANATION_METHODS.map((method) => (
              <label
                key={method.value}
                className={`flex cursor-pointer items-center gap-3 rounded-lg border p-3 ${
                  selectedMethods.includes(method.value)
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300 hover:border-blue-300"
                }`}
              >
                <input
                  type="checkbox"
                  value={method.value}
                  checked={selectedMethods.includes(method.value)}
                  onChange={() => handleMethodToggle(method.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <p className="font-medium">{method.label}</p>
                  <p className="text-xs text-gray-500">{method.description}</p>
                </div>
              </label>
            ))}
          </div>
          {selectedMethods.length === 0 && (
            <p className="text-sm text-red-500">
              Please select at least one explanation method
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            What kind of math problems do you prefer?
          </label>
          <div className="space-y-2">
            {CHALLENGE_OPTIONS.map((option) => (
              <label
                key={option.value}
                className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-300 p-3 hover:border-blue-500"
              >
                <input
                  type="radio"
                  value={option.value}
                  {...register("challenge_preference")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <p className="font-medium">{option.label}</p>
                  <p className="text-xs text-gray-500">{option.description}</p>
                </div>
              </label>
            ))}
          </div>
          {errors.challenge_preference && (
            <p className="text-sm text-red-500">
              {errors.challenge_preference.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            How long can you typically focus on math work before needing a
            break?
          </label>
          <div className="space-y-2">
            {ATTENTION_SPAN_OPTIONS.map((option) => (
              <label
                key={option.value}
                className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-300 p-3 hover:border-blue-500"
              >
                <input
                  type="radio"
                  value={option.value}
                  {...register("attention_span")}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                />
                <span>{option.label}</span>
              </label>
            ))}
          </div>
          {errors.attention_span && (
            <p className="text-sm text-red-500">
              {errors.attention_span.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label
            htmlFor="interests"
            className="block text-sm font-medium text-gray-700"
          >
            What are some of your interests? (Separate by commas)
          </label>
          <textarea
            id="interests"
            {...register("interests")}
            className="w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="E.g., sports, music, science, gaming"
            rows={3}
          ></textarea>
        </div>

        <div className="mt-8 flex justify-between">
          <button
            type="button"
            onClick={() => router.push("/student/intake/math-confidence")}
            className="rounded-md border border-gray-300 px-6 py-2 text-gray-700 hover:bg-gray-50"
          >
            Back
          </button>

          <button
            type="submit"
            disabled={selectedMethods.length === 0}
            className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 disabled:bg-blue-300"
          >
            Complete Profile
          </button>
        </div>
      </form>
    </div>
  );
}
