"use client";
import { useIntakeForm } from "@/contexts/intake-form-context";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import ProtectedRoute from "@/components/protected-route";
import {
  completeIntakeForm,
  type IntakeFormData,
} from "@/app/actions/complete-intake";

export default function IntakeComplete() {
  const router = useRouter();
  const { formData } = useIntakeForm();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState("");
  const hasSubmitted = useRef(false);

  useEffect(() => {
    const submitForm = async () => {
      if (!user || isSubmitted || hasSubmitted.current) return;
      hasSubmitted.current = true;
      setIsSubmitting(true);

      try {
        // Prepare data for server action with proper typing
        const intakeData: IntakeFormData = {
          name: formData.name,
          grade: formData.grade as "6th" | "7th" | "8th" | "9th",
          age: formData.age,
          languages: formData.languages,
          learning_style: formData.learning_style as
            | "visual"
            | "auditory"
            | "kinesthetic"
            | "reading_writing",
          challenge_preference: formData.challenge_preference as
            | "easy"
            | "moderate"
            | "difficult"
            | "applied",
          attention_span: formData.attention_span as
            | "short"
            | "medium"
            | "long",
          math_confidence: formData.math_confidence as
            | "not_confident"
            | "somewhat_unconfident"
            | "neutral"
            | "somewhat_confident"
            | "very_confident",
          challenging_topics: formData.challenging_topics || [],
          preferred_explanation_methods:
            formData.preferred_explanation_methods || [],
          interests: formData.interests || [],
        };

        // Call server action instead of direct database operations
        const result = await completeIntakeForm(intakeData);

        if (result.success) {
          setIsSubmitted(true);
          // Clear form data from localStorage after successful submission
          localStorage.removeItem("edengageIntakeForm");
        } else {
          setError(
            result.error || "Failed to save your profile. Please try again."
          );
        }
      } catch (error: unknown) {
        console.error("Intake submission error:", error);
        setError("An unexpected error occurred. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    };

    submitForm();
  }, [formData, user, isSubmitted]);

  return (
    <ProtectedRoute>
      <div className="flex h-full flex-col items-center justify-center">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-md">
          <div className="mb-6 flex justify-center">
            {isSubmitting ? (
              <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
            ) : (
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100 text-green-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
            )}
          </div>
          <h2 className="mb-2 text-center text-2xl font-bold">
            {isSubmitting
              ? "Submitting Your Profile..."
              : error
              ? "Error"
              : "Profile Complete!"}
          </h2>
          {error ? (
            <div className="mb-6 rounded-md bg-red-50 p-4 text-center text-red-700">
              {error}
              <div className="mt-4">
                <button
                  onClick={() => {
                    setError("");
                    setIsSubmitting(true);
                    setTimeout(() => {
                      window.location.reload();
                    }, 1000);
                  }}
                  className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <p className="mb-6 text-center text-gray-600">
              {isSubmitting
                ? "Please wait while we save your information."
                : "Thank you for completing your profile. We'll use this information to personalize your learning experience."}
            </p>
          )}
          {!isSubmitting && !error && (
            <div className="flex justify-center">
              <button
                onClick={() => router.push("/student/dashboard")}
                className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
              >
                Go to Dashboard
              </button>
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}
