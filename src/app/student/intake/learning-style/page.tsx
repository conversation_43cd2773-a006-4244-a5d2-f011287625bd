// src/app/student/intake/learning-style/page.tsx
"use client";

import { useIntakeForm } from "@/contexts/intake-form-context";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

// Learning style options
const LEARNING_STYLE_OPTIONS = [
  {
    value: "visual",
    label: "Through visual diagrams and images",
    description:
      "You learn best when information is presented in charts, diagrams, and visual formats.",
  },
  {
    value: "auditory",
    label: "By listening to explanations",
    description:
      "You prefer to hear information and learn through discussions and verbal instructions.",
  },
  {
    value: "kinesthetic",
    label: "By doing hands-on activities",
    description:
      "You learn best through practical experiences and physical activities.",
  },
  {
    value: "reading_writing",
    label: "By reading text and written explanations",
    description:
      "You prefer learning through reading and writing, with detailed text explanations.",
  },
];

// Form validation schema
const learningStyleSchema = z.object({
  learning_style: z.string().min(1, "Please select a learning style"),
});

type LearningStyleFormValues = z.infer<typeof learningStyleSchema>;

export default function LearningStylePage() {
  const router = useRouter();
  const { formData, updateFormData } = useIntakeForm();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LearningStyleFormValues>({
    resolver: zodResolver(learningStyleSchema),
    defaultValues: {
      learning_style: formData.learning_style,
    },
  });

  const onSubmit = (data: LearningStyleFormValues) => {
    updateFormData("learning_style", data.learning_style);
    router.push("/student/intake/math-confidence");
  };

  return (
    <div className="h-full">
      <h2 className="mb-2 text-2xl font-bold">Learning Style</h2>
      <p className="mb-8 text-gray-600">
        Help us understand how you prefer to learn
      </p>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 max-w-xl">
        <div className="space-y-4">
          {LEARNING_STYLE_OPTIONS.map((option) => (
            <label
              key={option.value}
              className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-300 p-4 hover:border-blue-500"
            >
              <input
                type="radio"
                value={option.value}
                {...register("learning_style")}
                className="h-5 w-5 text-blue-600 focus:ring-blue-500"
              />
              <div>
                <p className="font-medium">{option.label}</p>
                <p className="text-sm text-gray-500">{option.description}</p>
              </div>
            </label>
          ))}
        </div>

        {errors.learning_style && (
          <p className="text-sm text-red-500">
            {errors.learning_style.message}
          </p>
        )}

        <div className="mt-8 flex justify-between">
          <button
            type="button"
            onClick={() => router.push("/student/intake/basic-info")}
            className="rounded-md border border-gray-300 px-6 py-2 text-gray-700 hover:bg-gray-50"
          >
            Back
          </button>

          <button
            type="submit"
            className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
}
