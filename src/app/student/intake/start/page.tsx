// src/app/student/intake/start/page.tsx
"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { shouldRedirectFromIntake } from "@/utils/intake-utils";
import Image from "next/image";

export default function IntakeStart() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [checkingIntake, setCheckingIntake] = useState(true);

  useEffect(() => {
    async function checkIntakeStatus() {
      if (!isLoading && user) {
        const redirectPath = await shouldRedirectFromIntake(user.id);
        if (redirectPath) {
          // User has already completed intake, redirect to dashboard
          router.replace(redirectPath);
        } else {
          setCheckingIntake(false);
        }
      } else if (!isLoading) {
        // User not authenticated, let ProtectedRoute handle it
        setCheckingIntake(false);
      }
    }

    checkIntakeStatus();
  }, [user, isLoading, router]);

  const handleStart = () => {
    router.push("/student/intake/basic-info");
  };

  // Show loading while checking intake status
  if (isLoading || checkingIntake) {
    return (
      <div className="flex h-full flex-col items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Checking your profile...</p>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col items-center justify-center">
      <h1 className="mb-4 text-3xl font-bold">Welcome to EdEngage</h1>
      <p className="mb-8 text-center text-gray-600">
        Let's get to know you better so we can personalize your learning
        experience. This short questionnaire will help us understand your
        learning style and preferences.
      </p>
      <button
        onClick={handleStart}
        className="rounded-lg bg-blue-600 px-8 py-3 text-white hover:bg-blue-700"
      >
        Start Questionnaire
      </button>
    </div>
  );
}
