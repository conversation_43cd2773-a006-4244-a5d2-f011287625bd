// src/app/student/dashboard/page.tsx
"use client";

import ProtectedRoute from "@/components/protected-route";
import { useAuth } from "@/contexts/auth-context";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { getUserRole, UserRole } from "@/utils/roles";
import type React from "react"
import { supabase } from "@/utils/supabase/client";


export default function FeedbackSurvey() {
  const router = useRouter();
  const { user } = useAuth();
  const [question1, setQuestion1] = useState<number | null>(null)
  const [question2, setQuestion2] = useState<number | null>(null)
  const [question3, setQuestion3] = useState("")
  const [question4, setQuestion4] = useState("")
  const [question5, setQuestion5] = useState(false)
  const [missing, setMissing] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    if (question1 === null || question2 === null) {
      setMissing(true)
      return;
    }
    setMissing(false)

    const { error: insertError } = await supabase.from("student_feedback").insert({
      email: question5 ? user.email : "",
      q1: question1,
      q2: question2,
      q3: question3,
      q4: question4
    });

    if (insertError) {
      alert(insertError)
    } else {
      router.push('/')
    }      
  }

  const renderScaleQuestion = (
    questionNumber: number,
    value: number | null,
    setValue: (value: number) => void,
    leftLabel: string,
    rightLabel: string,
  ) => (
    <div className="space-y-4">
      <div className="flex items-center justify-center space-x-12">
        <span>{leftLabel}</span>
        <div className="flex items-center justify-center space-x-8">
          {[1, 2, 3, 4, 5].map((num) => (
            <label key={num} className="flex flex-col items-center space-y-1.5 cursor-pointer">
              <input
                type="radio"
                name={`question${questionNumber}`}
                value={num}
                checked={value === num}
                onChange={() => setValue(num)}
                className="w-6 h-6 text-[#0442b3] border-2 border-[#b2b2b2] focus:ring-[#0442b3] focus:ring-2"
              />
              <span className="text-sm font-medium">{num}</span>
            </label>
          ))}
        </div>
        <span>{rightLabel}</span>

      </div>
    </div>
  )

  const submitColors = (question1 !== null && question2 !== null)
    ? "bg-[#0442b3] hover:bg-[#03348F] cursor-pointer"
    : "bg-[#0442b3] opacity-75"

  return (
    <ProtectedRoute>
      <div className="flex flex-col min-h-screen bg-[#f5f5f5]">
        {/* Header */}
        <header className="flex items-center justify-centergit px-4 py-2 border-b border-[#e6e7ea] bg-white">
          <h1 className="text-lg font-semibold text-[#5d5d5d]">EdEngage Feedback Survey</h1>
          <div className="w-6 h-6"></div> {/* Spacer for alignment */}
        </header>

        {/* Main content */}
        <div className="flex-1 pt-6 pb-3">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-[#e6e7ea]">
              <div className="bg-[#0442b3] text-white px-6 py-4 rounded-t-lg">
                <h2 className="text-2xl font-semibold">Help us improve EdEngage!</h2>
                <p className="mt-2 opacity-90">Your feedback helps us make learning better for everyone.</p>
              </div>

              <form onSubmit={handleSubmit} className="px-8 pb-4 pt-6">
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-[#000000] mb-1.5">
                    1. Did EdEngage make learning feel more fun or interesting than your usual homework?
                  </h3>
                  <p className="text-sm text-[#5d5d5d] mb-4">
                    Use a 1 - 5 scale to answer (1 = much less engaging, 3 = about the same, 5 = much more engaging)
                  </p>
                  {renderScaleQuestion(1, question1, setQuestion1, "Much less engaging", "Much more engaging")}
                </div>

                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-[#000000] mb-1.5">
                    2. If you got stuck on a problem while using EdEngage, how likely were you to try to solve it with
                    help from Eden instead of looking up the answer somewhere else?
                  </h3>
                  <p className="text-sm text-[#5d5d5d] mb-4">
                    Use a 1 - 5 scale to answer (e.g. 1 = very likely to look it up, 5 = very likely to stick with
                    EdEngage).
                  </p>
                  {renderScaleQuestion(
                    2,
                    question2,
                    setQuestion2,
                    "Very likely to look it up",
                    "Very likely to stick with EdEngage",
                  )}
                </div>

                <div className="space-y-2 mb-4">
                  <h3 className="text-lg font-semibold text-[#000000]">
                    3. What part of EdEngage helped you the most?
                  </h3>
                  <textarea
                    value={question3}
                    onChange={(e) => setQuestion3(e.target.value)}
                    placeholder="Please share your thoughts here..."
                    className="w-full h-28 p-4 border border-[#b2b2b2] rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-[#0442b3] focus:border-transparent"
                  />
                </div>

                <div className="space-y-2 mb-4">
                  <h3 className="text-lg font-semibold text-[#000000]">
                    4. What part of EdEngage was confusing or could be better?
                  </h3>
                  <textarea
                    value={question4}
                    onChange={(e) => setQuestion4(e.target.value)}
                    placeholder="Please share your thoughts here..."
                    className="w-full h-28 p-4 border border-[#b2b2b2] rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-[#0442b3] focus:border-transparent"
                    rows={6}
                  />
                </div>

                <div className="space-y-1.5 mb-6">
                  <div className="flex items-center space-x-3">
                    <label htmlFor="question4" className="text-lg font-semibold text-[#000000] cursor-pointer">
                      5. Can we contact you at the email you signed up with?
                    </label>
                    <input
                      type="checkbox"
                      id="question4"
                      checked={question5}
                      onChange={(e) => setQuestion5(e.target.checked)}
                      className="w-5 h-5 text-[#0442b3] border-2 border-[#b2b2b2] rounded focus:ring-[#0442b3] focus:ring-2"
                    />
                  </div>
                  <p className="text-sm text-[#5d5d5d] mb-4">
                    Check the box if you would like to get involved with future EdEngage demos.
                    Feedback will remain anonymous if unchecked.
                  </p>
                </div>

                {/* Submit button */}
                <div className="flex flex-col justify-center items-center">
                  <button
                    type="submit"
                    className={`px-8 py-3 text-white rounded-lg font-medium transition-colors focus:outline-none ${submitColors}`}
                  >
                    Submit Feedback
                  </button>
                  {missing && <p className="text-red-800 mt-1">Please select an answer to questions 1 and 2!</p>}

                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}