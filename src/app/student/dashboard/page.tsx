// src/app/student/dashboard/page.tsx
"use client";

import ProtectedRoute from "@/components/protected-route";
import { useAuth } from "@/contexts/auth-context";
import { useIntakeForm } from "@/contexts/intake-form-context";
// import Image from "next/image"; // Removed unused import
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import RoleBadge from "@/components/role-badge";
import { getUserRole, UserRole } from "@/utils/roles";
import { getStudentProfileData } from "@/app/actions/get-user-profile"; // Updated import name

// Define a type for the profile data we expect
interface StudentProfile {
  name: string | null;
  grade: string | null;
  modality_preference: string | null;
  attention_span: string | null;
}

export default function Dashboard() {
  const { user, signOut } = useAuth();
  const { formData } = useIntakeForm(); // formData can still be useful for initial display after intake
  const router = useRouter();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [profileData, setProfileData] = useState<StudentProfile | null>(null); // State for all profile data
  const [isLoadingProfile, setIsLoadingProfile] = useState<boolean>(true); // Start with loading true
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  // Cache duration: 5 minutes
  const CACHE_DURATION = 5 * 60 * 1000;

  useEffect(() => {
    async function fetchProfile() {
      if (user) {
        // Check if we have cached data that's still fresh
        const now = Date.now();
        if (profileData && now - lastFetchTime < CACHE_DURATION) {
          return; // Use cached data
        }

        setIsLoadingProfile(true); // Set loading true when fetch starts
        const role = await getUserRole();
        setUserRole(role);

        const profileResult = await getStudentProfileData();
        if (profileResult.success && profileResult.data) {
          setProfileData(profileResult.data as StudentProfile);
          setLastFetchTime(now);
        } else {
          // If profile isn't found in DB, and intake form has some data, use that as a temporary fallback
          // This is mostly relevant if a user just completed intake and lands here before everything is synced
          if (
            formData.name ||
            formData.grade ||
            formData.learning_style ||
            formData.attention_span
          ) {
            // Check if any relevant formData exists
            setProfileData({
              name: formData.name,
              grade: formData.grade,
              modality_preference: formData.learning_style,
              attention_span: formData.attention_span,
            });
            setLastFetchTime(now);
          }
          console.error(
            "Failed to fetch student profile for dashboard:",
            profileResult.error
          );
        }
        setIsLoadingProfile(false); // Set loading false after fetch attempt
      }
    }

    if (isLoadingProfile) {
      fetchProfile();
    }

  }, [user, isLoadingProfile]); // Only depend on user to prevent unnecessary refetches

  const handleSignOut = async () => {
    await signOut();
    router.push("/");
  };

  const displayName = isLoadingProfile
    ? "Loading..."
    : profileData?.name || user?.email;

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-100">
        {/* Header */}
        <header className="bg-blue-900 shadow-md">
          <div className="container mx-auto flex items-center justify-between p-4">
            <div className="flex items-center">
              <button className="logoButton">EdEngage</button>
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-white">Welcome, {displayName}</span>
              <button
                onClick={handleSignOut}
                className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              >
                Sign Out
              </button>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="container mx-auto p-8">
          <h1 className="mb-8 text-3xl font-bold">Student Dashboard</h1>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Profile Card */}
            <div className="rounded-lg bg-white p-6 shadow-lg hover:-translate-y-1 hover:shadow-xl transition-all duration-200">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">Your Profile</h2>
                {userRole && <RoleBadge role={userRole} />}
              </div>
              {isLoadingProfile ? (
                <div className="space-y-2">
                  <p>Loading profile...</p>
                  {/* You can add skeleton loaders here for a better UX */}
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/5"></div>
                </div>
              ) : profileData ? (
                <div className="space-y-2">
                  <p>
                    <strong>Name:</strong> {profileData.name || "N/A"}
                  </p>
                  <p>
                    <strong>Grade:</strong> {profileData.grade || "N/A"}
                  </p>
                  <p>
                    <strong>Learning Style:</strong>{" "}
                    {profileData.modality_preference || "N/A"}
                  </p>
                  <p>
                    <strong>Attention Span:</strong>{" "}
                    {profileData.attention_span || "N/A"}
                  </p>
                </div>
              ) : (
                <p>
                  Could not load profile information. Please complete the intake
                  form if you have not.
                </p>
              )}
              {!isLoadingProfile && !profileData?.name && (
                <div className="mt-4">
                  <Link
                    href="/student/intake/start"
                    className="text-blue-600 hover:underline"
                  >
                    Complete Your Profile
                  </Link>
                </div>
              )}
              {!isLoadingProfile && profileData?.name && (
                <div className="mt-4">
                  <Link
                    href="/student/profile" // Assuming you have a page to view/edit full profile
                    className="text-blue-600 hover:underline"
                  >
                    View full profile
                  </Link>
                </div>
              )}
            </div>

            {/* Assignments Card */}
            <div className="rounded-lg bg-white p-6 shadow-lg hover:-translate-y-1 hover:shadow-xl transition-all duration-200">
              <h2 className="mb-4 text-xl font-bold">Assignments</h2>
              <p className="text-gray-600">Check your assigned work.</p>
              <div className="mt-4">
                <Link
                  href="/student/assignments"
                  className="inline-block rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 transition-colors"
                >
                  View Assignments
                </Link>
              </div>
            </div>

            {/* Progress Card */}
            <div className="rounded-lg bg-white p-6 shadow-lg hover:-translate-y-1 hover:shadow-xl transition-all duration-200">
              <h2 className="mb-4 text-xl font-bold">Your Progress</h2>
              <p className="text-gray-600">
                Your learning journey is just beginning.
              </p>
              <div className="mt-4">
                <div className="h-6 w-full rounded-full bg-gray-200">
                  <div
                    className="h-6 rounded-full bg-blue-600 text-xs font-medium text-white flex flex-column justify-center items-center"
                    style={{ width: "10%" }}
                  >
                    10%
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Admin/Teacher actions section - only shown to admins and teachers */}
          {userRole && (userRole === "admin" || userRole === "teacher") && (
            <div className="mt-8">
              <h2 className="mb-4 text-2xl font-bold">
                Administrative Actions
              </h2>
              <div className="rounded-lg bg-white p-6 shadow-lg">
                <div className="space-y-4">
                  <p className="text-gray-600">
                    As a{userRole === "admin" ? "n admin" : " teacher"}, you
                    have access to additional features.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Link
                      href="/admin/assignments/create"
                      className="block p-4 bg-blue-50 rounded-lg border border-blue-200 text-blue-700 hover:bg-blue-100"
                    >
                      Create New Assignment
                    </Link>
                    {userRole === "admin" && (
                      <Link
                        href="/admin/users"
                        className="block p-4 bg-purple-50 rounded-lg border border-purple-200 text-purple-700 hover:bg-purple-100"
                      >
                        Manage Users
                      </Link>
                    )}
                    <Link
                      href="/admin/students"
                      className="block p-4 bg-green-50 rounded-lg border border-green-200 text-green-700 hover:bg-green-100"
                    >
                      View All Students
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}
