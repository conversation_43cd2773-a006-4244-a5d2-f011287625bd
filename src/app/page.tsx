"use client";
import Link from "next/link";
import Image from "next/image";
import { useAuth } from "@/contexts/auth-context";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/utils/supabase/client";
import { getDefaultRedirectPath } from "@/lib/auth/auth-utils";

export default function Home() {
  const { user, userRole, isLoading } = useAuth();
  const router = useRouter();
  const [checkedAuth, setCheckedAuth] = useState(false);
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    async function handleAuthenticatedUser() {
      if (!isLoading && user) {
        setRedirecting(true);

        try {
          // Get user role from database if not available in context
          let currentUserRole = userRole;
          if (!currentUserRole) {
            const { data: profile } = await supabase
              .from("users")
              .select("role")
              .eq("id", user.id)
              .maybeSingle();
            currentUserRole = profile?.role;
          }

          if (currentUserRole === "student") {
            // Check if student has completed intake
            const { data: studentProfile } = await supabase
              .from("student_profiles")
              .select("name, grade")
              .eq("user_id", user.id)
              .maybeSingle();

            if (
              !studentProfile ||
              !studentProfile.name ||
              !studentProfile.grade
            ) {
              // Student hasn't completed intake, redirect to intake form
              router.push("/student/intake/start");
            } else {
              // Student has completed intake, redirect to dashboard
              router.push("/student/dashboard");
            }
          } else if (currentUserRole) {
            // For teachers and admins, redirect to their respective dashboards
            const redirectPath = getDefaultRedirectPath(currentUserRole as any);
            router.push(redirectPath);
          } else {
            // No role found, redirect to intake (default to student)
            router.push("/student/intake/start");
          }
        } catch (error) {
          console.error("Error checking user profile:", error);
          // On error, default to student intake
          router.push("/student/intake/start");
        }
      } else if (!isLoading) {
        setCheckedAuth(true);
      }
    }

    handleAuthenticatedUser();
  }, [isLoading, user, userRole, router]);

  // While checking auth or redirecting, show spinner
  if (isLoading || !checkedAuth || redirecting) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {redirecting ? "Redirecting to your dashboard..." : "Loading..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <main className="relative min-h-screen bg-gray-100">
      {/* Logo top left */}
      <div className="absolute left-8 top-8">
        <button className="logoButton">EdEngage</button>
      </div>

      {/* Main content center */}
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="w-full max-w-2xl rounded-lg bg-white p-8 shadow-md">
          <div className="flex flex-col items-center text-center">
            <h1 className="mb-4 text-5xl font-bold text-blue-500">Welcome</h1>
            <h2 className="mb-8 text-2xl font-semibold text-gray-700">
              EdEngage Product Demo
            </h2>
            <p className="mb-10 text-gray-500 max-w-md">
              Experience how AI can personalize learning for every student. Get
              started instantly!
            </p>
            <Link
              href="/auth/magic-link"
              className="flex flex-col items-center justify-center rounded-lg bg-blue-900 p-8 text-white transition-transform hover:scale-105 shadow-lg"
            >
              <div className="mb-4 flex h-20 w-20 items-center justify-center rounded bg-blue-800 p-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
              </div>
              <span className="text-xl font-bold">Sign in or Register</span>
              <span className="mt-2 text-sm text-gray-200">
                Receive a secure link in your inbox
              </span>
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
