@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
}

.fadeTab {
  transition:
    width 0.7s,
    opacity 0.35s,
    padding 0.7s;
  overflow: hidden;
}

.hiddenFade {
  opacity: 0;
  width: 0;
  padding: 0;
  border: none;
}

.showingFade {
  opacity: 1;
  width: calc(var(--spacing) * 90);
}

.chatStepAnswer {
  width: min(max(40%, 550px), 100%);
}

.chatTitle {
  width: 340px;
  margin-left: calc(50% - 20px - 24px - calc(340px / 2));
  margin-right: auto;
}

.logoButton {
  font-size: 1.5em;
  padding: 5px 2px;
  border-radius: 10px 0;
  background-color: var(--color-blue-900, #102F82);
  color: white;
}