import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON><PERSON>_San<PERSON> } from "next/font/google";
import "./globals.css";
import { IntakeFormProvider } from "@/contexts/intake-form-context";
import { AuthProvider } from "@/contexts/auth-context";
import { TRPCProvider } from "@/utils/trpc-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const noto = Noto_Sans({ 
  subsets: ['latin'],
  weight: ['300', '400', '600', '700', '800']
 });

export const metadata: Metadata = {
  title: "Ed Engage Demo",
  description: "The AI Revolution in EdTech is here",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased ${noto.className}`}
      >
        <TRPCProvider>
          <AuthProvider>
            <IntakeFormProvider>{children}</IntakeFormProvider>
          </AuthProvider>
        </TRPCProvider>
      </body>
    </html>
  );
}
