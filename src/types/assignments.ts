// src/types/assignment.ts

export interface ChatMessage {
  id: string;
  sender: "student" | "EDEN";
  text: string;
  timestamp: Date;

  //Optional context for the message

  assignmentId?: string;
  questionId?: string;
  stepId?: string; // Could be the ID of the current step input field?
  student_vote?: boolean | null;
}

export interface StepInputDefinition {
  id: string; // e.g., "s1", "s2"
  label: string;
  placeholder: string;
}

export interface AssignmentQuestion {
  id: string; // e.g., "q1"
  problemStatement: string; // e.g., "Solve the following system of linear equations for {variables[0]} and {variables[1]}:"
  equations: string[]; // e.g., ["2x + y = 10", "x - y = 2"]
  variables: [string, string]; // e.g., ["x", "y"]
  steps: StepInputDefinition[]; // Predefined steps for the student to fill out

  // correctAnswer?: { [key: string]: string }; // e.g., { x: "4", y: "2" } if we want to validate later
}

export interface Assignment {
  id: string; // e.g., "linear-equations-8th-grade-1"
  title: string;
  description: string;
  gradeLevel: number;
  subject: string;
  questions: AssignmentQuestion[];
}

// To store student's progress/answers for an assignment
export interface StudentAssignmentAnswers {
  [questionId: string]: {
    steps: { [stepId: string]: string };
    final: { [variableName: string]: string };
  };
}
