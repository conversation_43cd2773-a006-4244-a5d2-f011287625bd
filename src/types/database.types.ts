// src/types/database.types.ts
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      students: {
        Row: {
          student_id: string;
          name: string;
          grade: string;
          registration_date: string;
          is_active: boolean;
        };
        Insert: {
          student_id: string;
          name: string;
          grade: string;
          registration_date?: string;
          is_active?: boolean;
        };
        Update: {
          student_id?: string;
          name?: string;
          grade?: string;
          registration_date?: string;
          is_active?: boolean;
        };
        Relationships: [];
      };
      student_profiles: {
        Row: {
          profile_id: string;
          student_id: string;
          effective_from: string;
          effective_to: string | null;
          attention_span: string;
          engagement_level: string | null;
          modality_preference: string;
          challenge_preference: string;
          tutoring_style_preference: string;
          optimal_difficulty_level: number;
          collaboration_preference: string | null;
          created_at: string;
          updated_at: string;
          created_by: string | null;
        };
        Insert: {
          profile_id?: string;
          student_id: string;
          effective_from: string;
          effective_to?: string | null;
          attention_span: string;
          engagement_level?: string | null;
          modality_preference: string;
          challenge_preference: string;
          tutoring_style_preference: string;
          optimal_difficulty_level: number;
          collaboration_preference?: string | null;
          created_at?: string;
          updated_at?: string;
          created_by?: string | null;
        };
        Update: {
          profile_id?: string;
          student_id?: string;
          effective_from?: string;
          effective_to?: string | null;
          attention_span?: string;
          engagement_level?: string | null;
          modality_preference?: string;
          challenge_preference?: string;
          tutoring_style_preference?: string;
          optimal_difficulty_level?: number;
          collaboration_preference?: string | null;
          created_at?: string;
          updated_at?: string;
          created_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "student_profiles_student_id_fkey";
            columns: ["student_id"];
            referencedRelation: "students";
            referencedColumns: ["student_id"];
          }
        ];
      };
      student_interests: {
        Row: {
          id: number;
          profile_id: string;
          interest: string;
        };
        Insert: {
          id?: number;
          profile_id: string;
          interest: string;
        };
        Update: {
          id?: number;
          profile_id?: string;
          interest?: string;
        };
        Relationships: [
          {
            foreignKeyName: "student_interests_profile_id_fkey";
            columns: ["profile_id"];
            referencedRelation: "student_profiles";
            referencedColumns: ["profile_id"];
          }
        ];
      };
      preferred_explanation_methods: {
        Row: {
          id: number;
          profile_id: string;
          method: string;
        };
        Insert: {
          id?: number;
          profile_id: string;
          method: string;
        };
        Update: {
          id?: number;
          profile_id?: string;
          method?: string;
        };
        Relationships: [
          {
            foreignKeyName: "preferred_explanation_methods_profile_id_fkey";
            columns: ["profile_id"];
            referencedRelation: "student_profiles";
            referencedColumns: ["profile_id"];
          }
        ];
      };
      personality_notes: {
        Row: {
          id: number;
          profile_id: string;
          communication_style: string;
          self_assessment_ability: string;
        };
        Insert: {
          id?: number;
          profile_id: string;
          communication_style: string;
          self_assessment_ability: string;
        };
        Update: {
          id?: number;
          profile_id?: string;
          communication_style?: string;
          self_assessment_ability?: string;
        };
        Relationships: [
          {
            foreignKeyName: "personality_notes_profile_id_fkey";
            columns: ["profile_id"];
            referencedRelation: "student_profiles";
            referencedColumns: ["profile_id"];
          }
        ];
      };
      academic_performance: {
        Row: {
          id: number;
          profile_id: string;
          type: string;
          topic: string;
          average_score: number | null;
          correct_first_try_percentage: number | null;
          missed_first_try_percentage: number | null;
          notes: string | null;
          created_at: string;
        };
        Insert: {
          id?: number;
          profile_id: string;
          type: string;
          topic: string;
          average_score?: number | null;
          correct_first_try_percentage?: number | null;
          missed_first_try_percentage?: number | null;
          notes?: string | null;
          created_at?: string;
        };
        Update: {
          id?: number;
          profile_id?: string;
          type?: string;
          topic?: string;
          average_score?: number | null;
          correct_first_try_percentage?: number | null;
          missed_first_try_percentage?: number | null;
          notes?: string | null;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "academic_performance_profile_id_fkey";
            columns: ["profile_id"];
            referencedRelation: "student_profiles";
            referencedColumns: ["profile_id"];
          }
        ];
      };
      affective_profiles: {
        Row: {
          affective_id: string;
          profile_id: string;
          interest_level: number | null;
          self_efficacy_score: number | null;
          goal_orientation: string | null;
          mindset: string | null;
          math_anxiety: string | null;
          reaction_to_failure: string | null;
        };
        Insert: {
          affective_id?: string;
          profile_id: string;
          interest_level?: number | null;
          self_efficacy_score?: number | null;
          goal_orientation?: string | null;
          mindset?: string | null;
          math_anxiety?: string | null;
          reaction_to_failure?: string | null;
        };
        Update: {
          affective_id?: string;
          profile_id?: string;
          interest_level?: number | null;
          self_efficacy_score?: number | null;
          goal_orientation?: string | null;
          mindset?: string | null;
          math_anxiety?: string | null;
          reaction_to_failure?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "affective_profiles_profile_id_fkey";
            columns: ["profile_id"];
            referencedRelation: "student_profiles";
            referencedColumns: ["profile_id"];
          }
        ];
      };
      knowledge_profiles: {
        Row: {
          knowledge_id: string;
          profile_id: string;
          progress_trend: string | null;
        };
        Insert: {
          knowledge_id?: string;
          profile_id: string;
          progress_trend?: string | null;
        };
        Update: {
          knowledge_id?: string;
          profile_id?: string;
          progress_trend?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "knowledge_profiles_profile_id_fkey";
            columns: ["profile_id"];
            referencedRelation: "student_profiles";
            referencedColumns: ["profile_id"];
          }
        ];
      };
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          role: "student" | "teacher" | "admin";
          school_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          role?: "student" | "teacher" | "admin";
          school_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          role?: "student" | "teacher" | "admin";
          school_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "users_id_fkey";
            columns: ["id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
