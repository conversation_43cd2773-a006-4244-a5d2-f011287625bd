// src/lib/auth/auth-utils.ts
// Shared utilities that can be used on both client and server

export type UserRole = "student" | "teacher" | "admin";

// Route access patterns
export const ROUTE_PATTERNS = {
  public: ["/", "/auth/magic-link", "/auth/callback"],
  student: ["/student/**"],
  teacher: ["/teacher/**", "/student/**"], // Teachers can access student routes
  admin: ["/admin/**", "/teacher/**", "/student/**"], // Ad<PERSON> can access all routes
} as const;

// Get default redirect path for user role
export function getDefaultRedirectPath(role: UserRole | null): string {
  switch (role) {
    case "admin":
      return "/admin/dashboard";
    case "teacher":
      return "/teacher/dashboard";
    case "student":
      return "/student/dashboard";
    default:
      return "/auth/magic-link";
  }
}

// Check if user can access a specific route (client-side version)
export function canAccessRoute(userRole: UserRole, pathname: string): boolean {
  // Public routes are accessible to everyone
  if (ROUTE_PATTERNS.public.some(pattern => 
    pattern === pathname || 
    (pattern.endsWith("/**") && pathname.startsWith(pattern.slice(0, -3)))
  )) {
    return true;
  }
  
  // Check role-specific access
  const allowedPatterns = ROUTE_PATTERNS[userRole];
  return allowedPatterns.some(pattern => 
    pattern === pathname || 
    (pattern.endsWith("/**") && pathname.startsWith(pattern.slice(0, -3)))
  );
}

// Role hierarchy utilities
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  student: 1,
  teacher: 2,
  admin: 3,
};

export function hasRoleLevel(userRole: UserRole, requiredRole: UserRole): boolean {
  const userLevel = ROLE_HIERARCHY[userRole];
  const requiredLevel = ROLE_HIERARCHY[requiredRole];
  return userLevel >= requiredLevel;
}

export function hasAnyOfRoles(userRole: UserRole, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(userRole);
}
