// src/lib/auth/auth-service.ts
import { createClient } from "@/utils/supabase/server";
import { User } from "@supabase/supabase-js";
import { cache } from "react";
import {
  type UserRole,
  ROLE_HIERARCHY,
  hasRoleLevel,
  hasAnyOfRoles,
  getDefaultRedirectPath,
  ROUTE_PATTERNS,
} from "./auth-utils";

// Re-export for convenience
export type { UserRole };
export { getDefaultRedirectPath };

// Enhanced user type with role information
export interface AuthUser extends User {
  role?: UserRole;
}

// Cache the auth user lookup to avoid repeated database calls
export const getAuthUser = cache(async (): Promise<AuthUser | null> => {
  try {
    const supabase = await createClient();

    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return null;
    }

    // Fetch user role from the database
    const { data: profile, error: profileError } = await supabase
      .from("users")
      .select("role")
      .eq("id", user.id)
      .maybeSingle();

    if (profileError) {
      console.error("Error fetching user profile:", profileError);
      return user; // Return user without role if profile fetch fails
    }

    // Return user with role information
    return {
      ...user,
      role: profile?.role as UserRole,
    };
  } catch (error) {
    console.error("Error in getAuthUser:", error);
    return null;
  }
});

// Get user role only (cached)
export const getUserRole = cache(async (): Promise<UserRole | null> => {
  const user = await getAuthUser();
  return user?.role || null;
});

// Role checking utilities
export async function hasRole(requiredRole: UserRole): Promise<boolean> {
  const userRole = await getUserRole();
  if (!userRole) return false;
  return hasRoleLevel(userRole, requiredRole);
}

export async function hasAnyRole(roles: UserRole[]): Promise<boolean> {
  const userRole = await getUserRole();
  return userRole ? hasAnyOfRoles(userRole, roles) : false;
}

export async function isAdmin(): Promise<boolean> {
  return hasRole("admin");
}

export async function isTeacher(): Promise<boolean> {
  return hasRole("teacher");
}

export async function isStudent(): Promise<boolean> {
  const userRole = await getUserRole();
  return userRole === "student";
}

// Check if user can access a specific route
export async function canAccessRoute(pathname: string): Promise<boolean> {
  const userRole = await getUserRole();

  // Public routes are accessible to everyone
  if (
    ROUTE_PATTERNS.public.some(
      (pattern) =>
        pattern === pathname ||
        (pattern.endsWith("/**") && pathname.startsWith(pattern.slice(0, -3)))
    )
  ) {
    return true;
  }

  if (!userRole) return false;

  // Check role-specific access
  const allowedPatterns = ROUTE_PATTERNS[userRole];
  return allowedPatterns.some(
    (pattern) =>
      pattern === pathname ||
      (pattern.endsWith("/**") && pathname.startsWith(pattern.slice(0, -3)))
  );
}
