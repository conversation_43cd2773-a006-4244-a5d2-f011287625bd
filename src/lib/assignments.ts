// src/lib/assignments.ts
import type { Assignment } from "../types/assignments";

export const mockAssignments: Assignment[] = [
  {
    id: "linear-equations-8th-grade-1",
    title: "Linear Equations Practice 1 (8th Grade)",
    description:
      "Solve 5 systems of linear equations. Show your work for each step and provide the final values for the variables.",
    gradeLevel: 8,
    subject: "Math",
    questions: [
      {
        id: "q1",
        problemStatement:
          "Solve the following system of linear equations for {vars[0]} and {vars[1]}:",
        equations: ["2x + y = 10", "x - y = 2"],
        variables: ["x", "y"],
        steps: [
          {
            id: "s1",
            label: "Step 1: Isolate a variable in one equation.",
            placeholder: "e.g., y = 10 - 2x",
          },
          {
            id: "s2",
            label: "Step 2: Substitute this into the other equation.",
            placeholder: "e.g., x - (10 - 2x) = 2",
          },
          {
            id: "s3",
            label: "Step 3: Solve for the first variable.",
            placeholder: "e.g., 3x = 12  => x = 4",
          },
          {
            id: "s4",
            label: "Step 4: Substitute back to find the second variable.",
            placeholder: "e.g., y = 10 - 2(4) => y = 2",
          },
        ],
      },
      {
        id: "q2",
        problemStatement: "Solve for {vars[0]} and {vars[1]}:",
        equations: ["3a - 2b = 7", "a + 4b = 15"],
        variables: ["a", "b"],
        steps: [
          {
            id: "s1",
            label: "Step 1: Isolate a variable.",
            placeholder: "e.g., a = 15 - 4b",
          },
          {
            id: "s2",
            label: "Step 2: Substitute.",
            placeholder: "e.g., 3(15 - 4b) - 2b = 7",
          },
          {
            id: "s3",
            label: "Step 3: Solve for one variable.",
            placeholder:
              "e.g., 45 - 12b - 2b = 7 => -14b = -38 => b = 19/7 or 2.71",
          },
          {
            id: "s4",
            label: "Step 4: Solve for the other variable.",
            placeholder:
              "e.g., a = 15 - 4(19/7) => a = (105-76)/7 => a = 29/7 or 4.14",
          },
        ],
      },
      {
        id: "q3",
        problemStatement: "Find the values of {vars[0]} and {vars[1]}:",
        equations: ["5m + 3n = 1", "2m - n = -4"],
        variables: ["m", "n"],
        steps: [
          {
            id: "s1",
            label: "Isolate one variable.",
            placeholder: "e.g., n = 2m + 4",
          },
          {
            id: "s2",
            label: "Substitute into the other equation.",
            placeholder: "e.g., 5m + 3(2m + 4) = 1",
          },
          {
            id: "s3",
            label: "Solve for the first variable identified.",
            placeholder: "e.g., 5m + 6m + 12 = 1 => 11m = -11 => m = -1",
          },
          {
            id: "s4",
            label: "Solve for the remaining variable.",
            placeholder: "e.g., n = 2(-1) + 4 => n = 2",
          },
        ],
      },
      {
        id: "q4",
        problemStatement: "Determine {vars[0]} and {vars[1]} for the system:",
        equations: ["x + y = 6", "x = y + 2"],
        variables: ["x", "y"],
        steps: [
          {
            id: "s1",
            label: "Step 1: Identify an isolated variable or isolate one.",
            placeholder: "e.g., x = y + 2 (given)",
          },
          {
            id: "s2",
            label: "Step 2: Substitute into the other equation.",
            placeholder: "e.g., (y + 2) + y = 6",
          },
          {
            id: "s3",
            label: "Step 3: Solve for one variable.",
            placeholder: "e.g., 2y + 2 = 6 => 2y = 4 => y = 2",
          },
          {
            id: "s4",
            label: "Step 4: Solve for the other variable.",
            placeholder: "e.g., x = 2 + 2 => x = 4",
          },
        ],
      },
      {
        id: "q5",
        problemStatement: "Calculate {vars[0]} and {vars[1]}:",
        equations: ["4x - 3y = 11", "2x + 5y = -1"],
        variables: ["x", "y"],
        steps: [
          {
            id: "s1",
            label: "Isolate one variable from one equation.",
            placeholder: "e.g., 2x = -1 - 5y => x = (-1 - 5y)/2",
          },
          {
            id: "s2",
            label: "Substitute this expression into the other equation.",
            placeholder: "e.g., 4((-1 - 5y)/2) - 3y = 11",
          },
          {
            id: "s3",
            label: "Solve the resulting equation for the first variable.",
            placeholder:
              "e.g., 2(-1 - 5y) - 3y = 11 => -2 - 10y - 3y = 11 => -13y = 13 => y = -1",
          },
          {
            id: "s4",
            label: "Substitute the value back to find the second variable.",
            placeholder:
              "e.g., x = (-1 - 5(-1))/2 => x = (-1 + 5)/2 => x = 4/2 => x = 2",
          },
        ],
      },
    ],
  },
];

export function getAssignmentById(id: string): Assignment | undefined {
  return mockAssignments.find((assignment) => assignment.id === id);
}
