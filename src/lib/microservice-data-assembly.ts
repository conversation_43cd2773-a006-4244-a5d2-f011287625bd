import { createClient } from "@supabase/supabase-js";

// Types for microservice payloads
export interface ChatPayload {
  student_profile: any;
  problem: any;
  chat_history: any[];
  user_message: string;
}

export interface SocraticPayload {
  student_explanation: string;
  problem: any;
  correct_steps: string[];
  chat_history: any[];
  student_profile: any;
  steps_completed: number[];
}

export interface ChatResponse {
  reply: string;
  moderation_flagged: boolean;
  moderation_response: string;
  agent_outputs: any;
}

export interface SocraticResponse {
  reply: string;
  current_step: number;
  steps_covered: number[];
  next_expected: number;
  all_steps_sufficient: boolean;
  step_scores: any[];
  agent_outputs: any;
}

/**
 * Assembles student profile data for microservice endpoints
 */
export async function assembleStudentProfile(supabase: any, userId: string) {
  try {
    // Get basic student profile
    const { data: profile, error: profileError } = await supabase
      .from("student_profiles")
      .select("*")
      .eq("user_id", userId)
      .single();

    if (profileError) throw profileError;

    // Get affective profile
    const { data: affectiveProfile, error: affectiveError } = await supabase
      .from("student_affective_profiles")
      .select("*")
      .eq("user_id", userId)
      .single();

    // Get interests
    const { data: interests, error: interestsError } = await supabase
      .from("student_interests")
      .select("interest_name")
      .eq("user_id", userId);

    // Get explanation preferences
    const { data: explanationPrefs, error: explanationError } = await supabase
      .from("student_explanation_preferences")
      .select("method_name")
      .eq("user_id", userId);

    // Get mastery levels
    const { data: masteryLevels, error: masteryError } = await supabase
      .from("student_mastery")
      .select("standard_code, mastery_level")
      .eq("user_id", userId);

    // Assemble the complete profile
    const studentProfile = {
      student_id: userId,
      student_profile: {
        name: profile?.name || "Student",
        grade: profile?.grade || "8th",
        attention_span: profile?.attention_span || "medium",
        engagement_level: profile?.engagement_level || "medium",
        modality_preference: profile?.modality_preference || "visual",
        interests: interests?.map((i) => i.interest_name) || [],
        preferred_explanation_methods: explanationPrefs?.map(
          (p) => p.method_name
        ) || ["real-world examples"],
        challenge_preference: profile?.challenge_preference || "applied",
        tutoring_style_preference:
          profile?.tutoring_style_preference || "contextual learning",
        optimal_difficulty_level: profile?.optimal_difficulty_level || 5,
        collaboration_preference:
          profile?.collaboration_preference ||
          "Prefers independent or one-on-one interactions",
        personality_notes: {
          communication_style: "To be determined based on interactions",
          self_assessment_ability: "To be determined based on interactions",
        },
        academic_performance: {
          self_reported_strengths: profile?.strengths || [
            "Geometry",
            "Graphing",
          ],
          self_reported_weaknesses: profile?.weaknesses || [
            "Algebra",
            "Word Problems",
          ],
        },
      },
      affective_profile: {
        interest_level: affectiveProfile?.interest_level || 3,
        self_efficacy_score: affectiveProfile?.self_efficacy_score || 0.8,
        goal_orientation: affectiveProfile?.goal_orientation || "mastery",
        mindset: affectiveProfile?.mindset || "strong growth",
        math_anxiety:
          affectiveProfile?.math_anxiety_level === "low"
            ? "None"
            : affectiveProfile?.math_anxiety_level || "None",
        reaction_to_failure: "persistent problem-solver",
      },
      teacher_inputs: {
        teacher_notes: "Initial profile based on student intake quiz",
        accommodations: [],
        reading_level: `Grade ${(profile?.grade || "8th").replace("th", "")}`,
        ELL_status: false,
        learning_environment_needs: [
          `Attention span: ${profile?.attention_span || "medium"}`,
        ],
      },
      session_history: [
        {
          date: "2025-05-02",
          topic: "Droid Delivery Mission",
          overall_understanding_score: 0.2,
          strengths: [],
          weaknesses: ["Algebra", "Equation formulation"],
          steps: [
            {
              step_description:
                "Write equations for each droid based on their starting distances and speeds.",
              hard_skills: ["Algebra", "Equation formulation"],
              soft_skills: ["Problem-solving", "Critical thinking"],
              step_score: 0.2,
            },
          ],
          highest_comprehension_chat: {
            student_message: "I dont know where to start",
            score: 0.2,
          },
          lowest_comprehension_chat: {
            student_message: "I dont know where to start",
            score: 0.2,
          },
          number_of_student_chats: 1,
          engagement_score: "medium",
          total_student_messages: 1,
        },
      ],
      indicators: {
        peer_percentile: 50,
        on_grade_level: true,
        flagged_attention_points: {},
        alerts: [],
      },
    };

    // Add mastery levels if available
    if (masteryLevels && masteryLevels.length > 0) {
      const masteryObj = masteryLevels.reduce((acc, item) => {
        acc[item.standard_code] = item.mastery_level;
        return acc;
      }, {} as Record<string, number>);
      (studentProfile.student_profile as any).mastery_levels = masteryObj;
    }

    return studentProfile;
  } catch (error) {
    console.error("Error assembling student profile:", error);
    throw error;
  }
}

/**
 * Assembles chat history for microservice endpoints
 * Now filters by both session_id and problem_id for question-specific chat history
 */
export async function assembleChatHistory(
  supabase: any,
  sessionId: string,
  problemId?: string,
  problemStatement?: string
) {
  try {
    let query = supabase
      .from("chat_messages")
      .select("*")
      .eq("session_id", sessionId);

    // Filter by problem_id if provided to get question-specific chat history
    if (problemId) {
      query = query.eq("problem_id", problemId);
    }

    const { data: messages, error } = await query.order("timestamp", {
      ascending: true,
    });

    if (error) throw error;

    // Always start with a single system message containing the problem context (if provided)
    let chatHistory: { role: "system" | "user" | "assistant"; content: string }[] = [];
    if (problemStatement) {
      chatHistory.push({
        role: "system",
        content: problemStatement,
      });
    }

    // Map the chat into OpenAI-style user/assistant turns (no duplicates/mis-titled roles)
    if (messages) {
      for (const msg of messages) {
        // Map sender_type from db ("student", "ai", "system") to OpenAI roles
        if (msg.sender_type === "student") {
          chatHistory.push({ role: "user", content: msg.content });
        } else if (msg.sender_type === "ai") {
          chatHistory.push({ role: "assistant", content: msg.content });
        }
        // Optionally: ignore other sender_types or handle them as needed
      }
    }

    // Optionally: Limit to last N turns (plus system message) for context size
    const SYSTEM_MSG_COUNT = chatHistory.length > 0 && chatHistory[0].role === "system" ? 1 : 0;
    const RECENT_TURNS = 20; // tune for your model context window
    if (chatHistory.length > RECENT_TURNS + SYSTEM_MSG_COUNT) {
      chatHistory = [
        ...chatHistory.slice(0, SYSTEM_MSG_COUNT),
        ...chatHistory.slice(-RECENT_TURNS),
      ];
    }

    return chatHistory;
  } catch (error) {
    console.error("Error assembling chat history:", error);
    throw error;
  }
}

/**
 * Gets problem data in microservice format
 */
export async function getProblemForMicroservice(
  supabase: any,
  problemId: string
) {
  try {
    const { data: problem, error } = await supabase
      .from("problems")
      .select("microservice_payload, standard_code, statement, title")
      .eq("id", problemId)
      .single();

    if (error) throw error;

    const payload = problem?.microservice_payload || {};

    // Transform the payload to match expected microservice schema
    let transformedPayload = {
      question: payload.statement || problem?.statement || "",
      steps: payload.steps || [],
      answer: payload.answer || "",
      standard: problem?.standard_code || "",
    };

    // For the Droid Delivery Mission, use the full formatted question
    if (problem?.title === "Droid Delivery Mission") {
      transformedPayload = {
        question: `🚀 Droid Delivery Mission:

Two Star Wars droids are sent from different rebel outposts to deliver data chips to a drop-off point.
  • Droid A starts 2 miles away from the drop‑off point and moves 1 mile every hour.
  • Droid B starts 8 miles away and moves 2 miles every hour.
Let y represent the distance each droid is from the drop‑off point after x hours.

After how many hours will both droids be the same distance from the drop‑off point?`,
        steps: [
          "Write an equation for the distance each droid is from the drop-off point as a function of time.",
          "Set the equations equal to each other to determine when they are at the same distance.",
          "Solve the resulting equation for x.",
          "Interpret the value of x in the context of the problem.",
        ],
        answer: "x = 6",
        standard: "NC.8.EE.8",
      };
    }

    return transformedPayload;
  } catch (error) {
    console.error("Error getting problem for microservice:", error);
    throw error;
  }
}

/**
 * Assembles complete payload for /chat endpoint
 */
export async function assembleChatPayload(
  supabase: any,
  userId: string,
  sessionId: string,
  problemId: string,
  userMessage: string
): Promise<ChatPayload> {
  try {
    const [studentProfile, problem] = await Promise.all([
      assembleStudentProfile(supabase, userId),
      getProblemForMicroservice(supabase, problemId),
    ]);

    // Get chat history with problem statement for system message
    // Now includes problemId to filter for question-specific chat history
    const chatHistory = await assembleChatHistory(
      supabase,
      sessionId,
      problemId,
      problem.question
    );

    return {
      student_profile: studentProfile,
      problem: problem,
      chat_history: chatHistory,
      user_message: userMessage,
    };
  } catch (error) {
    console.error("Error assembling chat payload:", error);
    throw error;
  }
}

/**
 * Assembles complete payload for /socratic endpoint
 */
export async function assembleSocraticPayload(
  supabase: any,
  userId: string,
  sessionId: string,
  problemId: string,
  studentExplanation: string,
  stepsCompleted: number[] = []
): Promise<SocraticPayload> {
  try {
    const [studentProfile, chatHistory, problem] = await Promise.all([
      assembleStudentProfile(supabase, userId),
      assembleChatHistory(supabase, sessionId, problemId), // Now includes problemId for question-specific chat
      getProblemForMicroservice(supabase, problemId),
    ]);

    // Get correct steps from problem
    const correctSteps = problem.steps || [];

    return {
      student_explanation: studentExplanation,
      problem: problem,
      correct_steps: correctSteps,
      chat_history: chatHistory,
      student_profile: studentProfile,
      steps_completed: stepsCompleted,
    };
  } catch (error) {
    console.error("Error assembling socratic payload:", error);
    throw error;
  }
}

/**
 * Stores chat response in database
 */
export async function storeChatResponse(
  supabase: any,
  sessionId: string,
  messageId: string,
  response: ChatResponse
) {
  try {
    const { data, error } = await supabase
      .from("chat_responses")
      .insert({
        session_id: sessionId,
        message_id: messageId,
        reply: response.reply,
        moderation_flagged: response.moderation_flagged,
        moderation_response: response.moderation_response,
        agent_outputs: response.agent_outputs,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error storing chat response:", error);
    throw error;
  }
}

/**
 * Stores socratic response in database
 */
export async function storeSocraticResponse(
  supabase: any,
  sessionId: string,
  stepWorkId: string | null,
  response: SocraticResponse
) {
  try {
    const { data, error } = await supabase
      .from("socratic_responses")
      .insert({
        session_id: sessionId,
        step_work_id: stepWorkId,
        reply: response.reply,
        current_step: response.current_step,
        steps_covered: response.steps_covered,
        next_expected: response.next_expected,
        all_steps_sufficient: response.all_steps_sufficient,
        step_scores: response.step_scores,
        agent_outputs: response.agent_outputs,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error storing socratic response:", error);
    throw error;
  }
}
