// src/contexts/auth-context.tsx
"use client";

import { User } from "@supabase/supabase-js";
import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/utils/supabase/client";
import { type UserRole } from "@/lib/auth/auth-utils";

interface AuthContextType {
  user: User | null;
  userRole: UserRole | null;
  isLoading: boolean;
  signInWithMagicLink: (email: string) => Promise<{ error: any; data: any }>;
  signInWithOtp: (
    email: string | null,
    phone: string | null
  ) => Promise<{ error: any; data: any }>;
  signOut: () => Promise<{ error: any }>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const router = useRouter();

  // Function to fetch user role
  const fetchUserRole = async (userId: string): Promise<UserRole | null> => {
    try {
      const { data: profile } = await supabase
        .from("users")
        .select("role")
        .eq("id", userId)
        .maybeSingle();

      return (profile?.role as UserRole) || null;
    } catch (error) {
      console.error("Error fetching user role:", error);
      return null;
    }
  };

  // Function to refresh auth state
  const refreshAuth = async (silent = false) => {
    if (!silent) {
      setIsLoading(true);
    }
    try {
      const { data } = await supabase.auth.getSession();
      const currentUser = data.session?.user || null;
      setUser(currentUser);

      if (currentUser) {
        const role = await fetchUserRole(currentUser.id);
        setUserRole(role);
      } else {
        setUserRole(null);
      }
    } catch (error) {
      console.error("Error refreshing auth:", error);
      setUser(null);
      setUserRole(null);
    } finally {
      if (!silent) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    // Initial auth check
    refreshAuth();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        const currentUser = session?.user || null;
        setUser(currentUser);

        if (currentUser) {
          const role = await fetchUserRole(currentUser.id);
          setUserRole(role);
        } else {
          setUserRole(null);
        }

        setIsLoading(false);
      }
    );

    // Cleanup function to remove the listener
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [router]);

  // Auth functions
  const signInWithMagicLink = async (email: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
          shouldCreateUser: true,
        },
      });

      return { data, error };
    } catch (error) {
      console.error("Magic link error:", error);
      return { data: null, error };
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithOtp = async (email: string | null, phone: string | null) => {
    setIsLoading(true);
    try {
      if (email) {
        const { data, error } = await supabase.auth.signInWithOtp({
          email,
          options: {
            shouldCreateUser: true,
          },
        });
        return { data, error };
      } else if (phone) {
        const { data, error } = await supabase.auth.signInWithOtp({
          phone,
          options: {
            shouldCreateUser: true,
          },
        });
        return { data, error };
      } else {
        return { data: null, error: new Error("Email or phone is required") };
      }
    } catch (error) {
      console.error("OTP error:", error);
      return { data: null, error };
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (!error) {
        setUser(null);
        setUserRole(null);
      }
      return { error };
    } catch (error) {
      console.error("Error signing out:", error);
      return { error };
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        userRole,
        isLoading,
        signInWithMagicLink,
        signInWithOtp,
        signOut: handleSignOut,
        refreshAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Create hook for using the context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
