// src/contexts/intake-form-context.tsx
"use client";

import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";


export const allowedLanguages = ['English', 'Spanish', 'French'] as const;
type AllowedLanguages = typeof allowedLanguages[number];

// Define the form data structure (matches server action schema)
export interface IntakeFormData {
  // Basic Info
  name: string;
  grade: "6th" | "7th" | "8th" | "9th" | string;
  age: number;
  languages: AllowedLanguages[];
    
  // Learning Style
  learning_style:
    | "visual"
    | "auditory"
    | "kinesthetic"
    | "reading_writing"
    | string;

  // Math Confidence
  math_confidence:
    | "not_confident"
    | "somewhat_unconfident"
    | "neutral"
    | "somewhat_confident"
    | "very_confident"
    | string;
  challenging_topics: string[];

  // Learning Preferences
  preferred_explanation_methods: string[];
  challenge_preference: "easy" | "moderate" | "difficult" | "applied" | string;
  attention_span: "short" | "medium" | "long" | string;
  interests: string[];
}

// Default empty form data
const defaultFormData: IntakeFormData = {
  name: "",
  grade: "",
  age: 10,
  languages: ["English"],
  learning_style: "",
  math_confidence: "",
  challenging_topics: [],
  preferred_explanation_methods: [],
  challenge_preference: "",
  attention_span: "",
  interests: [],
};

// Create context
interface IntakeFormContextType {
  formData: IntakeFormData;
  updateFormData: <K extends keyof IntakeFormData>(
    field: K,
    value: IntakeFormData[K]
  ) => void;
  updateMultipleFields: (fields: Partial<IntakeFormData>) => void;
}

const IntakeFormContext = createContext<IntakeFormContextType | undefined>(
  undefined
);

// Create provider
export function IntakeFormProvider({ children }: { children: ReactNode }) {
  const [formData, setFormData] = useState<IntakeFormData>(defaultFormData);

  // Load saved form data from localStorage on initial render
  useEffect(() => {
    const savedData = localStorage.getItem("edengageIntakeForm");
    if (savedData) {
      try {
        setFormData(JSON.parse(savedData));
      } catch (error) {
        console.error("Failed to parse saved form data:", error);
      }
    }
  }, []);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("edengageIntakeForm", JSON.stringify(formData));
  }, [formData]);

  const updateFormData = <K extends keyof IntakeFormData>(
    field: K,
    value: IntakeFormData[K]
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const updateMultipleFields = (fields: Partial<IntakeFormData>) => {
    setFormData((prev) => ({
      ...prev,
      ...fields,
    }));
  };

  return (
    <IntakeFormContext.Provider
      value={{ formData, updateFormData, updateMultipleFields }}
    >
      {children}
    </IntakeFormContext.Provider>
  );
}

// Create hook for using the context
export function useIntakeForm() {
  const context = useContext(IntakeFormContext);
  if (context === undefined) {
    throw new Error("useIntakeForm must be used within an IntakeFormProvider");
  }
  return context;
}
