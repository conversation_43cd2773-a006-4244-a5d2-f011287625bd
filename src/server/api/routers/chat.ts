import { z } from "zod";
import {
  createTRPCRouter,
  protectedProcedure,
  studentProcedure,
} from "../trpc";
import { chatMessageSchema } from "../../types/chat";
import { AIAgentMicroserviceClient } from "../../lib/microservice-client";
import { TRPCError } from "@trpc/server";
import { createClient } from "@supabase/supabase-js";
import {
  assembleChatPayload,
  assembleSocraticPayload,
  storeChatResponse,
  storeSocraticResponse,
  type ChatResponse,
  type SocraticResponse,
} from "../../../lib/microservice-data-assembly";
import { observable } from "@trpc/server/observable";

// Simple ID generator
const generateRequestId = () => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const aiClient = new AIAgentMicroserviceClient();

// Create Supabase client helper
const getSupabaseClient = () => {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
};

export const chatRouter = createTRPCRouter({
  // New endpoint for chat microservice integration
  sendChatMessage: studentProcedure
    .input(
      z.object({
        sessionId: z.string(),
        problemId: z.string(),
        userMessage: z.string().min(1).max(1000),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { user } = ctx;
      const supabase = getSupabaseClient();

      try {
        // Store the user message in chat_messages table
        const { data: messageData, error: messageError } = await supabase
          .from("chat_messages")
          .insert({
            session_id: input.sessionId,
            problem_id: input.problemId,
            role: "student",
            sender_type: "student",
            content: input.userMessage,
          })
          .select()
          .single();

        if (messageError) throw messageError;

        // Assemble payload for microservice
        const chatPayload = await assembleChatPayload(
          supabase,
          user.id,
          input.sessionId,
          input.problemId,
          input.userMessage
        );

        // TODO: Call actual microservice here
        // For demo, return mock response
        // const mockResponse: ChatResponse = {
        //   reply:
        //     "I understand you're working on this problem. Let me help you break it down step by step. What specific part would you like to start with?",
        //   moderation_flagged: false,
        //   moderation_response: "",
        //   agent_outputs: {
        //     ethic: {
        //       agent: "ethic",
        //       output_raw: {
        //         low_effort: false,
        //         bypass: false,
        //         unrelated: false,
        //         abuse: false,
        //       },
        //     },
        //   },
        // };

        // Call microservice and handle response
        let rawResponse: Partial<ChatResponse> = {};

        console.log(chatPayload);

        try {
          const response = await fetch(
            "https://f3nn2xqmmi.us-east-2.awsapprunner.com/chat/",
            {
              method: "POST",
              body: JSON.stringify(chatPayload),
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          if (!response.ok) {
            throw new Error(
              `Microservice responded with status: ${response.status}`
            );
          }

          rawResponse = await response.json();
          console.log(
            "Raw microservice response:",
            JSON.stringify(rawResponse, null, 2)
          );
          // console.log(
          //   "Chat payload sent:",
          //   JSON.stringify(chatPayload, null, 2)
          // );
        } catch (microserviceError) {
          console.error("Microservice call failed:", microserviceError);
          // Use fallback response when microservice fails
          rawResponse = {
            reply:
              "I'm having trouble connecting to my AI systems right now. Please try again in a moment.",
            moderation_flagged: false,
            moderation_response: "",
            agent_outputs: {},
          };
        }

        // Validate and ensure required fields are present
        const validatedResponse: ChatResponse = {
          reply:
            rawResponse.reply ||
            "I'm processing your request. Please wait a moment.",
          moderation_flagged: rawResponse.moderation_flagged || false,
          moderation_response: rawResponse.moderation_response || "",
          agent_outputs: rawResponse.agent_outputs || {},
        };

        // Store the AI response
        await storeChatResponse(
          supabase,
          input.sessionId,
          messageData.id,
          validatedResponse
        );

        // Store AI message in chat_messages
        const { error: aiMessageError } = await supabase
          .from("chat_messages")
          .insert({
            session_id: input.sessionId,
            problem_id: input.problemId,
            role: "assistant",
            sender_type: "ai",
            content: validatedResponse.reply,
          });

        if (aiMessageError) {
          console.error("Error storing AI message:", aiMessageError);
          throw aiMessageError;
        }

        return {
          reply: validatedResponse.reply,
          messageId: messageData.id,
        };
      } catch (error) {
        console.error("Error in sendChatMessage:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process chat message",
        });
      }
    }),

  // Streaming endpoint for chat messages
  sendChatMessageStream: studentProcedure
    .input(
      z.object({
        sessionId: z.string(),
        problemId: z.string(),
        userMessage: z.string().min(1).max(1000),
      })
    )
    .subscription(async ({ ctx, input }) => {
      const { user } = ctx;
      const supabase = getSupabaseClient();

      return observable<{ type: string; data: any }>((emit) => {
        const processMessage = async () => {
          try {
            emit.next({
              type: "status",
              data: { message: "Processing your message..." },
            });

            // Store the user message in chat_messages table
            const { data: messageData, error: messageError } = await supabase
              .from("chat_messages")
              .insert({
                session_id: input.sessionId,
                problem_id: input.problemId,
                role: "student",
                sender_type: "student",
                content: input.userMessage,
              })
              .select()
              .single();

            if (messageError) throw messageError;

            emit.next({
              type: "user_message_stored",
              data: { messageId: messageData.id },
            });

            // Assemble payload for microservice
            const chatPayload = await assembleChatPayload(
              supabase,
              user.id,
              input.sessionId,
              input.problemId,
              input.userMessage
            );

            emit.next({
              type: "status",
              data: { message: "Calling AI microservice..." },
            });

            // Call microservice and handle response
            let rawResponse: Partial<ChatResponse> = {};

            try {
              const response = await fetch(
                "https://f3nn2xqmmi.us-east-2.awsapprunner.com/chat/",
                {
                  method: "POST",
                  body: JSON.stringify(chatPayload),
                  headers: {
                    "Content-Type": "application/json",
                  },
                }
              );

              if (!response.ok) {
                throw new Error(
                  `Microservice responded with status: ${response.status}`
                );
              }

              rawResponse = await response.json();
              emit.next({ type: "microservice_response", data: rawResponse });
            } catch (microserviceError) {
              console.error("Microservice call failed:", microserviceError);
              // Use fallback response when microservice fails
              rawResponse = {
                reply:
                  "I'm having trouble connecting to my AI systems right now. Please try again in a moment.",
                moderation_flagged: false,
                moderation_response: "",
                agent_outputs: {},
              };
              emit.next({ type: "fallback_response", data: rawResponse });
            }

            // Validate and ensure required fields are present
            const validatedResponse: ChatResponse = {
              reply:
                rawResponse.reply ||
                "I'm processing your request. Please wait a moment.",
              moderation_flagged: rawResponse.moderation_flagged || false,
              moderation_response: rawResponse.moderation_response || "",
              agent_outputs: rawResponse.agent_outputs || {},
            };

            // Stream the response word by word
            const words = validatedResponse.reply.split(" ");
            let currentText = "";

            for (let i = 0; i < words.length; i++) {
              currentText += (i > 0 ? " " : "") + words[i];
              emit.next({
                type: "streaming_text",
                data: {
                  text: currentText,
                  isComplete: i === words.length - 1,
                },
              });
              // Add a small delay to simulate streaming
              await new Promise((resolve) => setTimeout(resolve, 50));
            }

            // Store the AI response
            await storeChatResponse(
              supabase,
              input.sessionId,
              messageData.id,
              validatedResponse
            );

            // Store AI message in chat_messages
            const { error: aiMessageError } = await supabase
              .from("chat_messages")
              .insert({
                session_id: input.sessionId,
                role: "assistant",
                sender_type: "ai",
                content: validatedResponse.reply,
              });

            if (aiMessageError) {
              console.error(
                "Error storing AI message in streaming:",
                aiMessageError
              );
              throw aiMessageError;
            }

            emit.next({
              type: "complete",
              data: {
                reply: validatedResponse.reply,
                messageId: messageData.id,
                agentOutputs: validatedResponse.agent_outputs,
              },
            });

            emit.complete();
          } catch (error) {
            console.error("Error in streaming chat message:", error);
            emit.error(
              new TRPCError({
                code: "INTERNAL_SERVER_ERROR",
                message: "Failed to process chat message",
              })
            );
          }
        };

        processMessage();

        // Cleanup function
        return () => {
          // Any cleanup logic if needed
        };
      });
    }),

  // New endpoint for socratic microservice integration
  sendSocraticMessage: studentProcedure
    .input(
      z.object({
        sessionId: z.string(),
        problemId: z.string(),
        studentExplanation: z.string().min(1).max(2000),
        stepsCompleted: z.array(z.number()).optional().default([]),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { user } = ctx;
      const supabase = getSupabaseClient();

      try {
        // Store the student message in chat_messages table first
        const { error: messageError } = await supabase
          .from("chat_messages")
          .insert({
            session_id: input.sessionId,
            role: "student",
            sender_type: "student",
            content: input.studentExplanation,
          });

        if (messageError) throw messageError;

        // Store the student explanation in student_step_work table
        const { data: stepWorkData, error: stepWorkError } = await supabase
          .from("student_step_work")
          .insert({
            session_id: input.sessionId,
            problem_id: input.problemId,
            step_id: 1, // For demo, using step 1
            student_work: input.studentExplanation,
            status: "in_progress",
          })
          .select()
          .single();

        if (stepWorkError) throw stepWorkError;

        // Assemble payload for socratic microservice
        await assembleSocraticPayload(
          supabase,
          user.id,
          input.sessionId,
          input.problemId,
          input.studentExplanation,
          input.stepsCompleted
        );

        // TODO: Call actual microservice here
        // For demo, return mock response
        const mockResponse: SocraticResponse = {
          reply:
            "That's a good start! Can you explain what you think the equations for each object's distance should look like? What variables will you use?",
          current_step: 0,
          steps_covered: [],
          next_expected: 0,
          all_steps_sufficient: false,
          step_scores: [
            {
              step_index: 0,
              description: "Write an equation for ObjectA: yA = 2 - 1x",
              score: 0,
              sufficient: false,
              evidence:
                "The student did not mention this step in their explanation.",
            },
          ],
          agent_outputs: {
            ethic: {
              is_valid: true,
              output_raw: {
                low_effort: false,
                bypass: false,
                unrelated: false,
                abuse: false,
              },
            },
          },
        };

        // Store the socratic response
        await storeSocraticResponse(
          supabase,
          input.sessionId,
          stepWorkData.id,
          mockResponse
        );

        // Store AI message in chat_messages
        await supabase.from("chat_messages").insert({
          session_id: input.sessionId,
          role: "assistant",
          sender_type: "ai",
          content: mockResponse.reply,
        });

        return {
          reply: mockResponse.reply,
          currentStep: mockResponse.current_step,
          stepsCovered: mockResponse.steps_covered,
          nextExpected: mockResponse.next_expected,
          allStepsSufficient: mockResponse.all_steps_sufficient,
          stepWorkId: stepWorkData.id,
        };
      } catch (error) {
        console.error("Error in sendSocraticMessage:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process socratic message",
        });
      }
    }),

  sendMessage: protectedProcedure
    .input(chatMessageSchema.omit({ studentId: true, sessionId: true }))
    .mutation(async ({ ctx, input }) => {
      const { user, cache } = ctx;

      // Generate cache key based on message content and context
      const cacheKey = cache.generateKey({
        message: input.message,
        assignmentId: input.assignmentId,
        questionId: input.questionId,
        stepId: input.stepId,
      });

      // Check cache first
      const cachedResponse = cache.get<{ reply: string; metadata?: unknown }>(
        cacheKey
      );
      if (cachedResponse) {
        console.log("Returning cached response for key:", cacheKey);
        return cachedResponse;
      }

      try {
        // Prepare the request for the microservice
        const microserviceRequest = {
          studentData: {
            id: user.id,
            message: input.message,
            context: {
              assignmentId: input.assignmentId,
              questionId: input.questionId,
              stepId: input.stepId,
              previousMessages: input.context?.previousMessages || [],
            },
          },
          requestId: generateRequestId(),
          timestamp: new Date().toISOString(),
        };

        // Call the AI microservice
        const response = await aiClient.process(microserviceRequest);

        // Cache the response with a TTL based on the type of query
        const ttl = input.stepId ? 300000 : 600000; // 5 or 10 minutes
        cache.set(cacheKey, response, ttl);

        return response;
      } catch (error) {
        console.error("Error processing chat message:", error);

        // Return a fallback response if the AI service is unavailable
        if (error instanceof Error && error.message.includes("timeout")) {
          throw new TRPCError({
            code: "TIMEOUT",
            message:
              "The AI service is taking longer than expected. Please try again.",
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process your message. Please try again.",
        });
      }
    }),

  // Get conversation history for a specific assignment
  getConversationHistory: protectedProcedure
    .input(
      z.object({
        assignmentId: z.string(),
        limit: z.number().min(1).max(100).default(50),
      })
    )
    .query(async () => {
      // This would typically fetch from a database
      // For now, returning a placeholder
      return {
        messages: [],
        hasMore: false,
      };
    }),

  // Get problems for demo
  getProblems: studentProcedure.query(async () => {
    const supabase = getSupabaseClient();

    try {
      const { data: problems, error } = await supabase
        .from("problems")
        .select("id, title, statement, microservice_payload")
        .limit(10);

      if (error) throw error;

      return problems || [];
    } catch (error) {
      console.error("Error getting problems:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get problems",
      });
    }
  }),

  // Create or get tutoring session
  getOrCreateSession: studentProcedure
    .input(
      z.object({
        problemId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { user } = ctx;
      const supabase = getSupabaseClient();

      try {
        // Check for existing active session
        const { data: existingSession, error: sessionError } = await supabase
          .from("tutoring_sessions")
          .select("*")
          .eq("user_id", user.id)
          .eq("status", "active")
          .maybeSingle();

        if (sessionError) throw sessionError;

        if (existingSession) {
          return existingSession;
        }

        // Create new session
        const { data: newSession, error: createError } = await supabase
          .from("tutoring_sessions")
          .insert({
            user_id: user.id,
            status: "active",
            session_metadata: { problem_id: input.problemId },
          })
          .select()
          .single();

        if (createError) throw createError;

        return newSession;
      } catch (error) {
        console.error("Error getting/creating session:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get or create session",
        });
      }
    }),

  // Get chat history for a session and specific problem
  getChatHistory: studentProcedure
    .input(
      z.object({
        sessionId: z.string(),
        problemId: z.string().optional(),
      })
    )
    .query(async ({ input }) => {
      if (!input.sessionId) {
        return [];
      }
      const supabase = getSupabaseClient();

      try {
        let query = supabase
          .from("chat_messages")
          .select("*")
          .eq("session_id", input.sessionId);

        // Filter by problem_id if provided for question-specific chat history
        if (input.problemId) {
          query = query.eq("problem_id", input.problemId);
        }

        const { data: messages, error } = await query.order("timestamp", {
          ascending: true,
        });

        if (error) throw error;

        console.log(
          `Chat history for session ${input.sessionId} and problem ${input.problemId}:`,
          messages
        );
        return messages || [];
      } catch (error) {
        console.error("Error getting chat history:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get chat history",
        });
      }
    }),

  // Clear cache for testing purposes (admin only)
  clearCache: protectedProcedure
    .input(
      z.object({
        pattern: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      // Add admin check here
      // For now, just clear the cache
      if (input.pattern) {
        // Implement pattern-based clearing if needed
        return { cleared: 0 };
      }

      // Clear all cache
      return { cleared: "all" };
    }),
});
