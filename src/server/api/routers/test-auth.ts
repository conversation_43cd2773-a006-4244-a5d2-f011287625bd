// src/server/api/routers/test-auth.ts
import { z } from "zod";
import { 
  createTR<PERSON>Router, 
  publicProcedure, 
  protectedProcedure,
  adminProcedure,
  teacherProcedure,
  studentProcedure
} from "@/server/api/trpc";

export const testAuthRouter = createTRPCRouter({
  // Public endpoint - anyone can access
  public: publicProcedure
    .query(() => {
      return {
        message: "This is a public endpoint - anyone can access this",
        timestamp: new Date().toISOString(),
      };
    }),

  // Protected endpoint - requires authentication
  protected: protectedProcedure
    .query(({ ctx }) => {
      return {
        message: "This is a protected endpoint - requires authentication",
        user: {
          id: ctx.user.id,
          email: ctx.user.email,
          role: ctx.userRole,
        },
        timestamp: new Date().toISOString(),
      };
    }),

  // Student endpoint - requires student role or higher
  student: studentProcedure
    .query(({ ctx }) => {
      return {
        message: "This is a student endpoint - requires student role or higher",
        user: {
          id: ctx.user.id,
          email: ctx.user.email,
          role: ctx.userRole,
        },
        timestamp: new Date().toISOString(),
      };
    }),

  // Teacher endpoint - requires teacher role or higher
  teacher: teacherProcedure
    .query(({ ctx }) => {
      return {
        message: "This is a teacher endpoint - requires teacher role or higher",
        user: {
          id: ctx.user.id,
          email: ctx.user.email,
          role: ctx.userRole,
        },
        timestamp: new Date().toISOString(),
      };
    }),

  // Admin endpoint - requires admin role
  admin: adminProcedure
    .query(({ ctx }) => {
      return {
        message: "This is an admin endpoint - requires admin role",
        user: {
          id: ctx.user.id,
          email: ctx.user.email,
          role: ctx.userRole,
        },
        timestamp: new Date().toISOString(),
      };
    }),

  // Test mutation with role check
  updateUserRole: adminProcedure
    .input(z.object({
      userId: z.string(),
      newRole: z.enum(["student", "teacher", "admin"]),
    }))
    .mutation(async ({ input, ctx }) => {
      // This would normally update the user role in the database
      // For testing, we'll just return a success message
      return {
        message: `Admin ${ctx.user.email} would update user ${input.userId} to role ${input.newRole}`,
        success: true,
        timestamp: new Date().toISOString(),
      };
    }),

  // Test getting current user info
  getCurrentUser: protectedProcedure
    .query(({ ctx }) => {
      return {
        id: ctx.user.id,
        email: ctx.user.email,
        role: ctx.userRole,
        emailVerified: ctx.user.email_confirmed_at,
        createdAt: ctx.user.created_at,
      };
    }),
});
