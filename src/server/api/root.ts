import { createTR<PERSON>Router } from "./trpc";
import { chatRouter } from "./routers/chat";
import { testAuthRouter } from "./routers/test-auth";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  chat: chatRouter,
  testAuth: testAuthRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
