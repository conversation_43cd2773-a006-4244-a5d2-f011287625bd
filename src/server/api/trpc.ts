import { initTR<PERSON>, TRPCError } from "@trpc/server";
import { createClient } from "@/utils/supabase/server";
import superjson from "superjson";
import { ZodError } from "zod";
import { createHash } from "crypto";

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// Simple in-memory cache (can be replaced with Redis)
class SimpleCache {
  private cache = new Map<string, CacheEntry<unknown>>();

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  set<T>(key: string, data: T, ttl: number = 60000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  generateKey(params: unknown): string {
    const hash = createHash("sha256");
    hash.update(JSON.stringify(params));
    return hash.digest("hex");
  }
}

export const cache = new SimpleCache();

/**
 * This helper function creates the context for tRPC
 * We use a simpler approach for App Router compatibility
 */
export const createTRPCContext = async (opts?: {
  headers?: Headers | Record<string, string>;
}) => {
  // Get user from Supabase
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Get user role if user exists
  let userRole: string | null = null;
  if (user) {
    const { data: profile } = await supabase
      .from("users")
      .select("role")
      .eq("id", user.id)
      .maybeSingle();
    userRole = profile?.role || null;
  }

  return {
    user,
    userRole,
    cache,
    headers: opts?.headers,
  };
};

/**
 * This is where the tRPC API is initialized, connecting the context and transformer.
 */
const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * These are the pieces you use to build your tRPC API.
 */
export const createTRPCRouter = t.router;
export const publicProcedure = t.procedure;

// Middleware to ensure user is authenticated
const enforceUserIsAuthed = t.middleware(({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({
    ctx: {
      // infers the `user` as non-nullable
      user: ctx.user,
      userRole: ctx.userRole,
    },
  });
});

// Middleware to ensure user has a specific role
const enforceUserRole = (requiredRole: string) =>
  t.middleware(({ ctx, next }) => {
    if (!ctx.user) {
      throw new TRPCError({ code: "UNAUTHORIZED" });
    }
    if (!ctx.userRole) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "User role not found",
      });
    }

    // Define role hierarchy: admin > teacher > student
    const roleHierarchy: Record<string, number> = {
      student: 1,
      teacher: 2,
      admin: 3,
    };

    const userLevel = roleHierarchy[ctx.userRole];
    const requiredLevel = roleHierarchy[requiredRole];

    if (!userLevel || !requiredLevel || userLevel < requiredLevel) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Requires ${requiredRole} role or higher`,
      });
    }

    return next({
      ctx: {
        user: ctx.user,
        userRole: ctx.userRole,
      },
    });
  });

// Middleware to ensure user has any of the specified roles
const enforceUserHasAnyRole = (allowedRoles: string[]) =>
  t.middleware(({ ctx, next }) => {
    if (!ctx.user) {
      throw new TRPCError({ code: "UNAUTHORIZED" });
    }
    if (!ctx.userRole || !allowedRoles.includes(ctx.userRole)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Requires one of: ${allowedRoles.join(", ")}`,
      });
    }

    return next({
      ctx: {
        user: ctx.user,
        userRole: ctx.userRole,
      },
    });
  });

export const protectedProcedure = t.procedure.use(enforceUserIsAuthed);
export const adminProcedure = t.procedure
  .use(enforceUserIsAuthed)
  .use(enforceUserRole("admin"));
export const teacherProcedure = t.procedure
  .use(enforceUserIsAuthed)
  .use(enforceUserRole("teacher"));
export const studentProcedure = t.procedure
  .use(enforceUserIsAuthed)
  .use(enforceUserHasAnyRole(["student", "teacher", "admin"]));
