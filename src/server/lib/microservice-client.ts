import { LambdaClient, InvokeCommand } from "@aws-sdk/client-lambda";
import { z } from "zod";
import {
  type MicroserviceRequest,
  type AIResponse,
  aiResponseSchema,
} from "../types/chat";

// Configuration for the microservice client
interface MicroserviceConfig {
  lambdaFunctionName: string;
  region: string;
  timeout: number;
  retries: number;
}

export class AIAgentMicroserviceClient {
  private lambdaClient: LambdaClient;
  private config: MicroserviceConfig;

  constructor(config?: Partial<MicroserviceConfig>) {
    this.config = {
      lambdaFunctionName:
        process.env.AI_LAMBDA_FUNCTION_NAME || "ai-agent-pipeline",
      region: process.env.AWS_REGION || "us-east-1",
      timeout: 30000, // 30 seconds
      retries: 2,
      ...config,
    };

    this.lambdaClient = new LambdaClient({
      region: this.config.region,
      // AWS credentials will be picked up from environment or IAM role
    });
  }

  async sendMessage(request: MicroserviceRequest): Promise<AIResponse> {
    try {
      const command = new InvokeCommand({
        FunctionName: this.config.lambdaFunctionName,
        Payload: Buffer.from(JSON.stringify(request)),
        InvocationType: "RequestResponse",
      });

      const response = await this.lambdaClient.send(command);

      if (!response.Payload) {
        throw new Error("No payload received from Lambda function");
      }

      const payload = JSON.parse(new TextDecoder().decode(response.Payload));

      // Handle Lambda errors
      if (response.StatusCode !== 200 || payload.errorMessage) {
        throw new Error(
          `Lambda invocation failed: ${payload.errorMessage || "Unknown error"}`
        );
      }

      // Parse and validate the response
      const parsedResponse = aiResponseSchema.parse(payload);
      return parsedResponse;
    } catch (error) {
      console.error("Error invoking AI microservice:", error);

      // Retry logic could be implemented here
      if (error instanceof z.ZodError) {
        throw new Error(`Invalid response from AI service: ${error.message}`);
      }

      throw error;
    }
  }

  // Alternative method for local development using Docker
  async sendMessageLocal(request: MicroserviceRequest): Promise<AIResponse> {
    const dockerEndpoint =
      process.env.AI_DOCKER_ENDPOINT || "http://localhost:8080";

    try {
      const response = await fetch(`${dockerEndpoint}/process`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(this.config.timeout),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const parsedResponse = aiResponseSchema.parse(data);
      return parsedResponse;
    } catch (error) {
      console.error("Error calling local AI service:", error);
      throw error;
    }
  }

  // Wrapper method that uses local in development, Lambda in production
  async process(request: MicroserviceRequest): Promise<AIResponse> {
    const isLocal =
      process.env.NODE_ENV === "development" &&
      process.env.USE_LOCAL_AI === "true";

    return isLocal ? this.sendMessageLocal(request) : this.sendMessage(request);
  }
}
