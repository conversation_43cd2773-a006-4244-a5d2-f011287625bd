import { z } from "zod";

// Input schema for chat messages
export const chatMessageSchema = z.object({
  message: z.string().min(1).max(1000),
  assignmentId: z.string(),
  questionId: z.string(),
  stepId: z.string().optional(),
  studentId: z.string(),
  sessionId: z.string(),
  context: z
    .object({
      previousMessages: z
        .array(
          z.object({
            role: z.enum(["user", "assistant"]),
            content: z.string(),
            timestamp: z.string(),
          })
        )
        .optional(),
      studentProfile: z
        .object({
          grade: z.string().optional(),
          subject: z.string().optional(),
          learningStyle: z.string().optional(),
          metadata: z.record(z.unknown()).optional(),
        })
        .optional(),
    })
    .optional(),
});

// Response schema from AI microservice
export const aiResponseSchema = z.object({
  reply: z.string(),
  metadata: z
    .object({
      confidence: z.number().min(0).max(1),
      suggestedNextSteps: z.array(z.string()).optional(),
      detectedIntent: z.string().optional(),
      emotionalTone: z.string().optional(),
    })
    .optional(),
  cacheKey: z.string().optional(),
});

// Schema for microservice request
export const microserviceRequestSchema = z.object({
  studentData: z.object({
    id: z.string(),
    message: z.string(),
    context: z.object({
      assignmentId: z.string(),
      questionId: z.string(),
      stepId: z.string().optional(),
      previousMessages: z
        .array(
          z.object({
            role: z.enum(["user", "assistant"]),
            content: z.string(),
            timestamp: z.string(),
          })
        )
        .optional(),
    }),
  }),
  requestId: z.string(),
  timestamp: z.string(),
});

export type ChatMessage = z.infer<typeof chatMessageSchema>;
export type AIResponse = z.infer<typeof aiResponseSchema>;
export type MicroserviceRequest = z.infer<typeof microserviceRequestSchema>;
