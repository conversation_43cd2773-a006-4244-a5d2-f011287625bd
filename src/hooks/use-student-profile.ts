// src/hooks/use-student-profile.ts
"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useAuth } from "@/contexts/auth-context";
import { useIntakeForm } from "@/contexts/intake-form-context";
import { getStudentProfileData } from "@/app/actions/get-user-profile";
import { getUserRole, UserRole } from "@/utils/roles";

interface StudentProfile {
  name: string | null;
  grade: string | null;
  modality_preference: string | null;
  attention_span: string | null;
}

interface UseStudentProfileReturn {
  profileData: StudentProfile | null;
  userRole: UserRole | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastFetchTime: number;
}

export function useStudentProfile(): UseStudentProfileReturn {
  const { user } = useAuth();
  const { formData } = useIntakeForm();
  const [profileData, setProfileData] = useState<StudentProfile | null>(null);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const [retryCount, setRetryCount] = useState<number>(0);
  const fetchingRef = useRef<boolean>(false);

  // Cache profile data for 5 minutes to prevent unnecessary refetches
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  const MAX_RETRIES = 3;

  const fetchProfile = useCallback(
    async (forceRefresh = false) => {
      if (!user || fetchingRef.current) return;

      // Check if we have cached data and it's still fresh
      const now = Date.now();
      if (
        !forceRefresh &&
        profileData &&
        now - lastFetchTime < CACHE_DURATION
      ) {
        return;
      }

      fetchingRef.current = true;
      setIsLoading(true);
      setError(null);

      try {
        // Fetch user role and profile data in parallel
        const [role, profileResult] = await Promise.all([
          getUserRole(),
          getStudentProfileData(),
        ]);

        setUserRole(role);

        if (profileResult.success && profileResult.data) {
          setProfileData(profileResult.data as StudentProfile);
          setLastFetchTime(now);
          setRetryCount(0); // Reset retry count on success
        } else {
          // If profile isn't found in DB, and intake form has some data, use that as a temporary fallback
          if (
            formData.name ||
            formData.grade ||
            formData.learning_style ||
            formData.attention_span
          ) {
            setProfileData({
              name: formData.name,
              grade: formData.grade,
              modality_preference: formData.learning_style,
              attention_span: formData.attention_span,
            });
            setLastFetchTime(now);
          } else {
            setError(profileResult.error || "Profile not found");
          }

          console.error(
            "Failed to fetch student profile:",
            profileResult.error
          );

          // Implement retry logic for failed requests
          if (retryCount < MAX_RETRIES && !formData.name) {
            setTimeout(() => {
              setRetryCount((prev) => prev + 1);
              fetchProfile(true);
            }, 1000 * Math.pow(2, retryCount)); // Exponential backoff
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
        console.error("Error in fetchProfile:", err);

        // Implement retry logic for errors
        if (retryCount < MAX_RETRIES) {
          setTimeout(() => {
            setRetryCount((prev) => prev + 1);
            fetchProfile(true);
          }, 1000 * Math.pow(2, retryCount)); // Exponential backoff
        }
      } finally {
        setIsLoading(false);
        fetchingRef.current = false;
      }
    },
    [
      user,
      formData.name,
      formData.grade,
      formData.learning_style,
      formData.attention_span,
      profileData,
      lastFetchTime,
      retryCount,
    ]
  );

  // Initial data fetch - only depends on user to prevent unnecessary refetches
  useEffect(() => {
    if (user) {
      fetchProfile();
    } else {
      // Reset state when user is null
      setProfileData(null);
      setUserRole(null);
      setIsLoading(false);
      setError(null);
      setLastFetchTime(0);
      setRetryCount(0);
    }
  }, [user]); // Only depend on user

  const refetch = useCallback(() => {
    return fetchProfile(true);
  }, [fetchProfile]);

  return {
    profileData,
    userRole,
    isLoading,
    error,
    refetch,
    lastFetchTime,
  };
}
