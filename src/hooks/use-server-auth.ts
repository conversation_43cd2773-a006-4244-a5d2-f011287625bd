// src/hooks/use-server-auth.ts
import { redirect } from "next/navigation";
import { getAuthUser, getUserRole } from "@/lib/auth/auth-service";
import {
  type UserRole,
  getDefaultRedirectPath,
  ROLE_HIERARCHY,
} from "@/lib/auth/auth-utils";

/**
 * Get the current authenticated user on the server side
 * Returns null if not authenticated
 */
export async function getServerUser() {
  return await getAuthUser();
}

/**
 * Require authentication - redirects to login if not authenticated
 * Returns the authenticated user
 */
export async function requireAuth() {
  const user = await getAuthUser();

  if (!user) {
    redirect("/auth/magic-link");
  }

  return user;
}

/**
 * Require a specific role - redirects if user doesn't have the required role
 * Returns the authenticated user with the required role
 */
export async function requireRole(requiredRole: UserRole) {
  const user = await requireAuth();

  if (!user.role) {
    redirect("/auth/magic-link");
  }

  const userLevel = ROLE_HIERARCHY[user.role];
  const requiredLevel = ROLE_HIERARCHY[requiredRole];

  if (!userLevel || !requiredLevel || userLevel < requiredLevel) {
    // Redirect to user's default dashboard
    redirect(getDefaultRedirectPath(user.role));
  }

  return user;
}

/**
 * Require any of the specified roles
 * Returns the authenticated user if they have one of the allowed roles
 */
export async function requireAnyRole(allowedRoles: UserRole[]) {
  const user = await requireAuth();

  if (!user.role || !allowedRoles.includes(user.role)) {
    // Redirect to user's default dashboard
    redirect(getDefaultRedirectPath(user.role));
  }

  return user;
}

/**
 * Require admin role specifically
 */
export async function requireAdmin() {
  return await requireRole("admin");
}

/**
 * Require teacher role or higher (teacher or admin)
 */
export async function requireTeacher() {
  return await requireRole("teacher");
}

/**
 * Require student role or higher (any authenticated user)
 */
export async function requireStudent() {
  return await requireAnyRole(["student", "teacher", "admin"]);
}

/**
 * Check if user has a specific role without redirecting
 * Returns boolean indicating if user has the role
 */
export async function hasRole(requiredRole: UserRole): Promise<boolean> {
  const user = await getAuthUser();

  if (!user?.role) return false;

  const userLevel = ROLE_HIERARCHY[user.role];
  const requiredLevel = ROLE_HIERARCHY[requiredRole];

  return userLevel >= requiredLevel;
}

/**
 * Check if user has any of the specified roles without redirecting
 */
export async function hasAnyRole(allowedRoles: UserRole[]): Promise<boolean> {
  const user = await getAuthUser();
  return user?.role ? allowedRoles.includes(user.role) : false;
}

/**
 * Get user role without redirecting
 */
export async function getServerUserRole(): Promise<UserRole | null> {
  return await getUserRole();
}

/**
 * Redirect user to their appropriate dashboard based on role
 */
export async function redirectToDashboard() {
  const userRole = await getUserRole();
  redirect(getDefaultRedirectPath(userRole));
}
