## Project Structure


```
ai_microservice/
├── __init__.py
└── main.py
├── routers/
│   ├── __init__.py
│   ├── chat.py
│   ├── edify_session.py
│   ├── embedding.py
│   ├── generate_problem.py
│   ├── health.py
│   ├── materials.py
│   └── socratic.py
│   ├── __pycache__/
│   │   ├── __init__.cpython-312.pyc
│   │   ├── chat.cpython-312.pyc
│   │   ├── edify_session.cpython-312.pyc
│   │   ├── embedding.cpython-312.pyc
│   │   ├── generate_problem.cpython-312.pyc
│   │   ├── health.cpython-312.pyc
│   │   ├── materials.cpython-312.pyc
│   │   └── socratic.cpython-312.pyc
├── schemas/
├── __pycache__/
│   ├── __init__.cpython-312.pyc
│   └── main.cpython-312.pyc
```




---


### `main.py`


```python
from fastapi import FastAPI
from ai_microservice.routers import generate_problem, chat, socratic, edify_session, embedding, materials, health


app = FastAPI(
    title="EdTutoring AI Microservice",
    version="0.1.0",
    description="Orchestrates multi-agent, LLM-driven math tutoring operations"
)


from mangum import Mangum


handler = Mangum(app)


app.include_router(generate_problem.router, prefix="/generate-problem")
app.include_router(chat.router, prefix="/chat")
app.include_router(socratic.router, prefix="/socratic")
app.include_router(edify_session.router, prefix="/edify-session")
## app.include_router(embedding.router, prefix="/embedding")
## app.include_router(materials.router, prefix="/materials")
app.include_router(health.router, prefix="")  # For /health, /version, etc


# For dev:
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("ai_microservice.main:app", host="0.0.0.0", port=8000, reload=True)
```


---


### `__init__.py`


```python


```


---


### `routers\chat.py`


```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import logging


# Import core orchestrator utilities from your library
from main.config import settings
from main.utils.agent_manager import create_agents
from main.utils.profile_manager import save_student_profile
from main.utils.agent_processor import process_with_eval, process_with_eden, build_eden_prompt
from main.utils.input_validator import validate_student_input


router = APIRouter()


from main.agents.eden_agent import create_eden_agent
from main.agents.eval_agent import create_eval_agent
from main.agents.ethic_agent import create_ethic_agent


class ChatAskQuestionRequest(BaseModel):
    student_profile: Dict[str, Any]
    problem: Dict[str, Any]                # Expects "steps": [ ... ], "question": str, etc.
    chat_history: List[Dict[str, Any]]
    user_message: str


class ChatAskQuestionResponse(BaseModel):
    reply: str
    moderation_flagged: bool = False
    moderation_response: Optional[str] = None
    agent_outputs: Optional[Dict[str, Any]] = None


@router.post("/", response_model=ChatAskQuestionResponse)
def chat_ask_question(req: ChatAskQuestionRequest):
    """
    Main tutoring endpoint: ETHIC -> (if valid) EDEN -> EVAL.
    Records full dynamic input/output pairs for each agent for analytics and prompt improvement.
    """
    try:
        # Direct agent instantiation for this endpoint only
        eden_agent = create_eden_agent(
            model=settings.eden_model,
            ##assistant_id=settings.eden_assistant_id,
            temperature=0.4
        )
        eval_agent = create_eval_agent(
            model=settings.eval_model,
            ##assistant_id=settings.eval_assistant_id,
            temperature=0.2
        )
        ethic_agent = create_ethic_agent(
            model=settings.ethic_model,
            #assistant_id=settings.ethic_assistant_id,
            temperature=0.2
        )
        agents = {
            "eden": eden_agent,
            "eval": eval_agent,
            "ethic": ethic_agent
        }


        agent_outputs = {}


        # 1. ETHIC moderation
        ethic_prompt_dynamic = {
            "problem_context": req.problem.get("question", ""),
            "user_message": req.user_message
        }
        moderation = validate_student_input(
            agents, req.user_message, req.problem, req.student_profile, return_prompt=True
        )
        agent_outputs["ethic"] = {
            "agent": "ethic",
            "prompt_dynamic": ethic_prompt_dynamic,
            "output_raw": moderation.get("output_raw"),
            "parsed_output": moderation.get("parsed_output", moderation)
        }
        if not moderation.get("is_valid", True):
            return ChatAskQuestionResponse(
                reply=moderation["response"],
                moderation_flagged=True,
                moderation_response=moderation["response"],
                agent_outputs=agent_outputs
            )


        # 2. EDEN Socratic
        steps = req.problem.get("steps", [])
        eden_prompt_text = build_eden_prompt(
            student_input=req.user_message,
            problem_data=req.problem,
            student_profile_dict=req.student_profile,
            steps=steps,
            chat_history=req.chat_history
        )
        eden_result = process_with_eden(
            agents=agents,
            student_input=req.user_message,
            problem_data=req.problem,
            student_profile_dict=req.student_profile,
            steps=steps,
            chat_history=req.chat_history,
            return_prompt=True
        )
        eden_response = eden_result.get("response", eden_result)
        agent_outputs["eden"] = {
            "agent": "eden",
            "prompt_dynamic": eden_prompt_text,
            "output_raw": eden_result.get("llm_output_raw", eden_response),
            "parsed_output": eden_result.get("parsed_output", eden_response)
        }


        # 3. EVAL evaluation
        eval_prompt_dynamic = {
            "student_question": req.user_message,
            "tutor_response": eden_response,
            "problem": req.problem.get("question", "")
        }
        eval_result = process_with_eval(
            agents=agents,
            student_input=req.user_message,
            tutor_response=eden_response,
            problem_data=req.problem,
            return_prompt=True
        )
        agent_outputs["eval"] = {
            "agent": "eval",
            "prompt_dynamic": eval_prompt_dynamic,
            "output_raw": eval_result.get("llm_output_raw"),
            "parsed_output": eval_result.get("parsed_output", eval_result)
        }


        return ChatAskQuestionResponse(
            reply=eden_response,
            agent_outputs=agent_outputs
        )
    except Exception as e:
        logging.exception("Error in chat/ask-question endpoint")
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
```


---


### `routers\edify_session.py`


```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional


from main.agents.edify_agent import create_edify_agent
from main.models.output_schemas import EdifyOutput


router = APIRouter()


class EdifySessionRequest(BaseModel):
    chat_history: List[Dict[str, Any]]         # List of {"role": "...", "content": "..."}
    problem: Dict[str, Any]                    # Must include at least: "question", "answer", "steps" (optional: standard, etc.)


class EdifySessionResponse(BaseModel):
    session_summary: str
    strengths: List[str]
    weaknesses: List[str]
    misconceptions: List[str]
    learning_style_observations: str
    recommended_next_topics: List[str]
    teaching_strategy_recommendations: List[str]
    profile_updates: Dict[str, Any]
    formative_assessment_highlights: Optional[Dict[str, Any]] = None
    understanding_score: float


@router.post("/", response_model=EdifySessionResponse)
def edify_session_route(req: EdifySessionRequest):
    """
    Endpoint for session analysis and profile recommendations after session/assignment finishes.
    Returns structured EDIFY analysis and profile update suggestions based on session transcript and problem.
    """
    try:
        # 1. Create EDIFY agent
        agent = create_edify_agent()


        # 2. Build rich, labeled input for analysis. (You can make this even more robust if you wish.)
        # Input will include a transcript and problem with full fields.
        chat_segment = "\n\n".join([f"{m['role'].title()}: {m['content']}" for m in req.chat_history])
        prompt = f"""
TUTORING SESSION TRANSCRIPT:
{chat_segment}


PROBLEM:
Statement: {req.problem.get('question','')}
Answer: {req.problem.get('answer','')}
Expected Steps:
{chr(10).join([f"{i+1}. {s}" for i,s in enumerate(req.problem.get('steps',[]))])}


---
Analyze the above session as EDIFY. Use your schema and return ONLY your required JSON object.
"""


        # 3. Run agent and get validated EdifyOutput result
        from main.utils.agent_wrapper import universal_agent_process
        agent_output = universal_agent_process(agent, prompt, expected_output_schema=EdifyOutput)


        # 4. Defensive: Convert to dict for response
        if hasattr(agent_output, "dict"):
            agent_output = agent_output.dict()
        elif isinstance(agent_output, EdifyOutput):
            agent_output = agent_output.model_dump()


        # 5. Flatten fields for response, as per EDIFY output_pydantic
        return EdifySessionResponse(
            session_summary=agent_output["session_summary"],
            strengths=agent_output["strengths"],
            weaknesses=agent_output["weaknesses"],
            misconceptions=agent_output["misconceptions"],
            learning_style_observations=agent_output["learning_style_observations"],
            recommended_next_topics=agent_output["recommended_next_topics"],
            teaching_strategy_recommendations=agent_output["teaching_strategy_recommendations"],
            profile_updates=agent_output["profile_updates"],
            formative_assessment_highlights=agent_output.get("formative_assessment_highlights"),
            understanding_score=agent_output["understanding_score"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"EDIFY session processing failed: {str(e)}")
```


---


### `routers\embedding.py`


```python


```


---


### `routers\generate_problem.py`


```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
from main.agents.enrich_agent import create_enrich_agent


router = APIRouter()


class GenerateProblemRequest(BaseModel):
    student_profile: Dict[str, Any]
    interest: str  # one specific student interest
    original_question: str
    original_steps: List[str]
    original_answer: str


class GenerateProblemResponse(BaseModel):
    rewritten_question: str


@router.post("/", response_model=GenerateProblemResponse)
def generate_problem_route(req: GenerateProblemRequest):
    """
    Given a student, a specific INTEREST, and a bare math problem + solution,
    rewrite the question for engagement. The answer/steps must remain 100% unchanged.
    """
    try:
        # Setup agent and prompt
        enrich_agent = create_enrich_agent()
        # Build engineered input with explicit fields for clarity
        prompt = f"""
INTEREST: {req.interest}


ORIGINAL_QUESTION:
{req.original_question}


ORIGINAL_STEPS:
{chr(10).join([f"{i+1}. {step}" for i, step in enumerate(req.original_steps)])}


ORIGINAL_ANSWER:
{req.original_answer}


PROFILE:
{req.student_profile}
---
Using the guidelines, rewrite ONLY the problem statement as described.
"""
        # Call agent (synchronous): ideally via your universal agent processor:
        from main.utils.agent_wrapper import universal_agent_process
        rewritten_question = universal_agent_process(enrich_agent, prompt)
        if isinstance(rewritten_question, dict):
            # Defensive: agent was misprompted and returned a structured result
            rewritten_question = rewritten_question.get("question") or str(rewritten_question)
        return {"rewritten_question": rewritten_question}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in personalized problem generation: {str(e)}")
```


---


### `routers\health.py`


```python
from fastapi import APIRouter


router = APIRouter()


@router.get("/health")
def healthcheck():
    return {"status": "ok"}


@router.get("/version")
def version():
    return {"version": "0.1.0"}
```


---


### `routers\materials.py`


```python


```


---


### `routers\socratic.py`


```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional


from main.agents.ergo_agent import create_ergo_agent
from main.agents.ethic_agent import create_ethic_agent
from main.agents.eden_agent import create_eden_agent
from main.agents.eval_agent import create_eval_agent
from main.models.output_schemas import ErgoStepwiseOutput
from main.utils.input_validator import validate_student_input
from main.utils.agent_wrapper import universal_agent_process


router = APIRouter()


class SocraticRequest(BaseModel):
    student_explanation: str
    problem: Dict[str, Any]
    correct_steps: List[str]
    chat_history: List[Dict[str, Any]]
    student_profile: Optional[Dict[str, Any]] = None
    steps_completed: Optional[List[int]] = None


class SocraticResponse(BaseModel):
    reply: str
    current_step: int
    steps_covered: List[int]
    next_expected: Optional[int]
    all_steps_sufficient: bool = False
    step_scores: Optional[List[Dict[str, Any]]] = None
    agent_outputs: Optional[Dict[str, Any]] = None


def format_chat_history(history: List[Dict[str, Any]]) -> str:
    """Formats chat history for prompt insertion."""
    return "\n".join([f"{msg['role'].capitalize()}: {msg['content']}" for msg in history])


@router.post("/", response_model=SocraticResponse)
def socratic_route(req: SocraticRequest):
    try:
        # 1. ETHIC moderation
        ethic_agent = create_ethic_agent()
        moderation = validate_student_input(
            {"ethic": ethic_agent},
            req.student_explanation,
            req.problem,
            req.student_profile or {},
            return_prompt=True
        )
        agent_outputs = {"ethic": moderation}
        if not moderation.get("is_valid", True):
            return SocraticResponse(
                reply=moderation["response"],
                current_step=0,
                steps_covered=req.steps_completed or [],
                next_expected=None,
                all_steps_sufficient=False,
                step_scores=[],
                agent_outputs=agent_outputs
            )


        # 2. ERGO for stepwise scoring only
        ergo_agent = create_ergo_agent()
        transcript = req.chat_history + [{"role": "student", "content": req.student_explanation}]
        prompt = {
            "STEPS": req.correct_steps,
            "CHAT_HISTORY": transcript,
            "STUDENT_EXPLANATION": req.student_explanation
        }
        ergo_out: ErgoStepwiseOutput = universal_agent_process(
            ergo_agent, prompt, expected_output_schema=ErgoStepwiseOutput
        )
        if hasattr(ergo_out, "dict"):
            ergo_out = ergo_out.dict()
        agent_outputs["ergo"] = ergo_out


        scores = ergo_out["step_scores"]
        steps_covered = [s["step_index"] for s in scores if s["sufficient"]]
        unaddressed = [s["step_index"] for s in scores if not s["sufficient"]]
        current_focus = unaddressed[0] if unaddressed else None
        all_steps_sufficient = all(s["sufficient"] for s in scores)
        next_expected = current_focus


        # 3. EDEN SOCRATIC: step-targeted, safe context
        eden_agent = create_eden_agent()
        eval_agent = create_eval_agent()


        chat_history_str = format_chat_history(transcript)
        profile_str = str(req.student_profile) if req.student_profile else ""


        if all_steps_sufficient or current_focus is None:
            eden_prompt = (
                f"You are a Socratic math tutor.\n\n"
                f"Problem: {req.problem.get('statement','')}\n"
                f"The student has completed all required steps for this problem."
                f"\nTheir final explanation:\n\"{req.student_explanation}\"\n"
                f"\nYour ONLY job is to provide concise, genuine praise or invitation to reflect, WITHOUT introducing, hinting at, or restating any solution step math or process."
                f"\n\nFull chat history up to now:\n{chat_history_str}"
            )
        else:
            step_info = scores[current_focus]
            step_sufficient = step_info["sufficient"]


            if step_sufficient:
                eden_prompt = (
                    f"You are a Socratic math tutor. The student is working through this problem:\n"
                    f"{req.problem.get('statement','')}\n\n"
                    f"The student just wrote (for the current step):\n\"{req.student_explanation}\"\n"
                    f"\nTheir response is judged sufficient by a grader for this step, but you must NOT reference, hint at, or restate the actual math step, operation, or process itself."
                    f"\nONLY respond with genuine concise praise for the STUDENT'S OWN reasoning and (optionally) ask generally what they might do next—but NEVER give clues, reminders, or even the topic of the next step."
                    f"\nFull chat history below for context. Never leak or mention any unreached step or math content."
                    f"\n\n----\n{chat_history_str}"
                )
            else:
                eden_prompt = (
                    f"You are a Socratic math tutor. The student is solving this problem:\n"
                    f"{req.problem.get('statement','')}\n\n"
                    f"Current required step is being evaluated.\n"
                    f"The student just wrote:\n\"{req.student_explanation}\"\n"
                    f"\nTheir response is NOT sufficient to fully justify this step. Your job: encourage or ask the student to clarify, elaborate, or be more specific for THIS step ONLY, but NEVER state, restate, hint, or paraphrase the step, its math, or any solution content (even in general terms)."
                    f"\nProvide NO math, operations, or knowledge about solution steps whatsoever. Stay strictly Socratic. Use the full chat history below for context."
                    f"\n\n----\n{chat_history_str}"
                )


        eden_result = universal_agent_process(
            eden_agent, eden_prompt
        )
        if isinstance(eden_result, dict):
            eden_reply = eden_result.get("reply") or eden_result.get("response") or str(eden_result)
        else:
            eden_reply = eden_result
        agent_outputs["eden"] = eden_reply


        from main.utils.agent_processor import process_with_eval


        agents = {
            "eval": eval_agent
        }


        eval_result = process_with_eval(
            agents=agents,
            student_input=req.student_explanation,
            tutor_response=eden_reply,
            problem_data=req.problem,  # (this should be the entire problem dict, matching what chat endpoint does)
            return_prompt=True
        )
        agent_outputs["eval"] = {
            "agent": "eval",
            "prompt_dynamic": eval_result.get("prompt_dynamic"),
            "output_raw": eval_result.get("llm_output_raw"),
            "parsed_output": eval_result.get("parsed_output", eval_result),
        }


        return SocraticResponse(
            reply=eden_reply,
            current_step=current_focus if current_focus is not None else 0,
            steps_covered=steps_covered,
            next_expected=next_expected,
            all_steps_sufficient=all_steps_sufficient,
            step_scores=scores,
            agent_outputs=agent_outputs
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Socratic explanation processing failed: {str(e)}")
```


---


### `routers\__init__.py`


```python


```



