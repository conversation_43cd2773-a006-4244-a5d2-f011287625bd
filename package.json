{"name": "mvp-edengage", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-lambda": "^3.817.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.76.1", "@trpc/client": "^11.1.2", "@trpc/next": "^11.1.2", "@trpc/react-query": "^11.1.2", "@trpc/server": "^11.1.2", "clsx": "^2.1.1", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "superjson": "^2.2.2", "tailwind-merge": "^3.3.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5"}}