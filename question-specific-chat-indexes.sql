-- SQL to add indexes for question-specific chat history
-- Run this in your Supabase SQL editor

-- Add composite index for session_id and problem_id (if not already exists)
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_problem ON chat_messages(session_id, problem_id);

-- Add index for problem_id alone for efficient filtering
CREATE INDEX IF NOT EXISTS idx_chat_messages_problem_id ON chat_messages(problem_id);

-- Verify the indexes were created
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'chat_messages' 
    AND (indexname LIKE '%session_problem%' OR indexname LIKE '%problem_id%')
ORDER BY indexname;
