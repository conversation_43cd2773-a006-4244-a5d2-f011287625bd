-- =============================================
-- EDENGAGE FINAL OPTIMIZED DATABASE SCHEMA
-- Designed specifically for AI microservice endpoints
-- Supports: /generate-problem, /chat, /socratic, /edify-session
-- =============================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =============================================
-- CORE USER MANAGEMENT
-- =============================================

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    role TEXT NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'teacher', 'admin')),
    school_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- STUDENT PROFILES (Optimized for AI Microservice)
-- =============================================

-- Core student profile information
CREATE TABLE public.student_profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    
    -- Basic info
    name TEXT,
    grade TEXT,
    
    -- Learning preferences (for /generate-problem personalization)
    attention_span TEXT DEFAULT 'medium' CHECK (attention_span IN ('short', 'medium', 'long')),
    engagement_level TEXT DEFAULT 'moderate',
    modality_preference TEXT DEFAULT 'mixed' CHECK (modality_preference IN ('visual', 'auditory', 'kinesthetic', 'reading_writing', 'mixed')),
    challenge_preference TEXT DEFAULT 'moderate' CHECK (challenge_preference IN ('easy', 'moderate', 'difficult', 'applied')),
    tutoring_style_preference TEXT DEFAULT 'socratic' CHECK (tutoring_style_preference IN ('socratic', 'direct', 'step_by_step')),
    optimal_difficulty_level INTEGER DEFAULT 5 CHECK (optimal_difficulty_level >= 1 AND optimal_difficulty_level <= 10),
    collaboration_preference TEXT,
    
    -- Communication patterns (for agent adaptation)
    communication_style TEXT,
    self_assessment_ability TEXT,
    
    -- Performance tracking
    global_correct_first_try_rate NUMERIC(3,2) DEFAULT 0.0,
    global_skip_rate NUMERIC(3,2) DEFAULT 0.0,
    
    -- AI-generated insights
    ai_learning_patterns JSONB DEFAULT '{}',
    ai_recommendations JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student interests (many-to-many)
CREATE TABLE public.student_interests (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    interest_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, interest_name)
);

-- Preferred explanation methods
CREATE TABLE public.student_explanation_preferences (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    method_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, method_name)
);

-- Affective profile
CREATE TABLE public.student_affective_profiles (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    interest_level INTEGER DEFAULT 3 CHECK (interest_level >= 1 AND interest_level <= 5),
    self_efficacy_score NUMERIC(3,2) DEFAULT 0.5 CHECK (self_efficacy_score >= 0 AND self_efficacy_score <= 1),
    goal_orientation TEXT DEFAULT 'mastery' CHECK (goal_orientation IN ('mastery', 'performance', 'mixed')),
    mindset TEXT DEFAULT 'growth' CHECK (mindset IN ('growth', 'fixed', 'mixed')),
    math_anxiety_level TEXT DEFAULT 'moderate' CHECK (math_anxiety_level IN ('low', 'moderate', 'high')),
    reaction_to_failure TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teacher inputs and accommodations
CREATE TABLE public.teacher_student_notes (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    teacher_notes TEXT,
    reading_level TEXT,
    ell_status BOOLEAN DEFAULT FALSE,
    accommodations TEXT[],
    learning_environment_needs TEXT[],
    last_updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ACADEMIC PERFORMANCE & MASTERY TRACKING
-- =============================================

-- Math standards reference
CREATE TABLE public.math_standards (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code TEXT UNIQUE NOT NULL, -- e.g., "NC.8.EE.7"
    title TEXT NOT NULL,
    description TEXT,
    grade_level INTEGER NOT NULL,
    domain TEXT,
    cluster TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student mastery levels by standard
CREATE TABLE public.student_mastery (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    standard_code TEXT NOT NULL,
    mastery_level NUMERIC(3,2) NOT NULL DEFAULT 0.0 CHECK (mastery_level >= 0 AND mastery_level <= 1),
    confidence_level NUMERIC(3,2) DEFAULT 0.5 CHECK (confidence_level >= 0 AND confidence_level <= 1),
    attempts_count INTEGER DEFAULT 0,
    correct_count INTEGER DEFAULT 0,
    last_practiced TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, standard_code)
);

-- Student misconceptions (tracked by AI agents)
CREATE TABLE public.student_misconceptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    concept TEXT NOT NULL,
    misconception_description TEXT NOT NULL,
    confidence_score NUMERIC(3,2) DEFAULT 0.5, -- AI confidence in this misconception
    first_observed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_observed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    times_observed INTEGER DEFAULT 1,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'improving')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Problem-solving strategies
CREATE TABLE public.student_strategies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    strategy_name TEXT NOT NULL,
    effectiveness_score NUMERIC(3,2), -- How well this strategy works for the student
    usage_frequency INTEGER DEFAULT 1,
    last_observed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PROBLEMS & CONTENT (Optimized for /generate-problem)
-- =============================================

-- Problem bank
CREATE TABLE public.problems (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT,
    statement TEXT NOT NULL,
    problem_type TEXT NOT NULL CHECK (problem_type IN ('socratic_conceptual', 'mechanical_steps', 'multiple_choice', 'open_ended')),
    
    -- Academic classification
    standard_code TEXT,
    difficulty_level INTEGER DEFAULT 5 CHECK (difficulty_level >= 1 AND difficulty_level <= 10),
    grade_level INTEGER,
    
    -- Problem content structure
    correct_answer JSONB,
    solution_approach TEXT,
    expected_steps JSONB DEFAULT '[]', -- For /socratic endpoint
    hints JSONB DEFAULT '[]',
    
    -- AI generation metadata (from /generate-problem)
    ai_generated BOOLEAN DEFAULT FALSE,
    generation_metadata JSONB DEFAULT '{}',
    personalization_tags TEXT[] DEFAULT '{}',
    
    -- Usage analytics
    times_used INTEGER DEFAULT 0,
    avg_completion_time_minutes NUMERIC(5,2),
    success_rate NUMERIC(3,2),
    
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Problem steps (for step-by-step scaffolding)
CREATE TABLE public.problem_steps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    problem_id UUID NOT NULL REFERENCES problems(id) ON DELETE CASCADE,
    step_number INTEGER NOT NULL,
    step_description TEXT NOT NULL,
    step_type TEXT CHECK (step_type IN ('setup', 'calculation', 'verification', 'reflection')),
    expected_input TEXT,
    validation_criteria JSONB DEFAULT '{}',
    hints_for_step JSONB DEFAULT '[]',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(problem_id, step_number)
);

-- =============================================
-- ASSIGNMENTS & SESSIONS (Core Tutoring Flow)
-- =============================================

-- Assignments
CREATE TABLE public.assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    assignment_type TEXT DEFAULT 'practice' CHECK (assignment_type IN ('homework', 'quiz', 'practice', 'assessment')),
    
    -- Configuration
    time_limit_minutes INTEGER,
    randomize_problems BOOLEAN DEFAULT FALSE,
    socratic_mode_enabled BOOLEAN DEFAULT TRUE,
    
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student assignment instances
CREATE TABLE public.student_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assignment_id UUID NOT NULL REFERENCES assignments(id) ON DELETE CASCADE,
    
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'abandoned')),
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    total_time_minutes INTEGER,
    
    -- Final results (populated by /edify-session)
    final_score NUMERIC(5,2),
    session_summary JSONB, -- From EDIFY agent
    teacher_insights JSONB, -- From EDIFY agent
    profile_updates_applied JSONB, -- Profile changes from this assignment
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, assignment_id)
);

-- Assignment problems (links problems to assignments)
CREATE TABLE public.assignment_problems (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_assignment_id UUID NOT NULL REFERENCES student_assignments(id) ON DELETE CASCADE,
    problem_id UUID NOT NULL REFERENCES problems(id) ON DELETE CASCADE,
    
    sequence_order INTEGER NOT NULL,
    problem_type_override TEXT,
    status TEXT DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'skipped')),
    
    -- Performance
    score NUMERIC(5,2),
    attempts_count INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(student_assignment_id, sequence_order)
);

-- =============================================
-- TUTORING SESSIONS (Core for /chat endpoint)
-- =============================================

-- Individual tutoring sessions for each assignment problem
CREATE TABLE public.tutoring_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    assignment_problem_id UUID NOT NULL REFERENCES assignment_problems(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Session context
    current_mode TEXT DEFAULT 'socratic_dialogue' CHECK (current_mode IN ('socratic_dialogue', 'mechanical_steps', 'final_answer_check')),
    current_step_id UUID REFERENCES problem_steps(id),
    
    -- Session state
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused')),
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    
    -- Session analytics (updated throughout)
    total_messages INTEGER DEFAULT 0,
    student_message_count INTEGER DEFAULT 0,
    engagement_score NUMERIC(3,2),
    understanding_score NUMERIC(3,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- CHAT & CONVERSATION TRACKING (For /chat endpoint)
-- =============================================

-- Chat messages (complete conversation history)
CREATE TABLE public.chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES tutoring_sessions(id) ON DELETE CASCADE,
    problem_id UUID REFERENCES problems(id) ON DELETE CASCADE,

    -- Message content
    role TEXT NOT NULL CHECK (role IN ('student', 'assistant', 'system')),
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'dialogue' CHECK (message_type IN ('dialogue', 'hint', 'feedback', 'step_check', 'system_note')),

    -- Context
    step_context_id UUID REFERENCES problem_steps(id),
    mode_when_sent TEXT,
    
    -- AI Agent outputs (from /chat endpoint)
    agent_outputs JSONB DEFAULT '{}', -- Raw outputs from ERGO, EVAL, ETHIC, etc.
    profile_updates_suggested JSONB DEFAULT '{}', -- Profile updates from this interaction
    retrieved_materials JSONB DEFAULT '[]', -- Materials from EMBED agent
    
    -- Analytics (computed by agents)
    understanding_score NUMERIC(3,2), -- Student understanding level in this message
    engagement_indicator TEXT CHECK (engagement_indicator IN ('high', 'medium', 'low', 'disengaged')),
    misconceptions_flagged JSONB DEFAULT '[]',
    
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- STEP TRACKING (Optimized for /socratic endpoint)
-- =============================================

-- Student work on individual problem steps
CREATE TABLE public.student_step_work (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES tutoring_sessions(id) ON DELETE CASCADE,
    step_id UUID NOT NULL REFERENCES problem_steps(id) ON DELETE CASCADE,
    
    -- Student work
    attempt_number INTEGER DEFAULT 1,
    student_response TEXT,
    explanation_provided TEXT, -- For /socratic analysis
    
    -- AI Analysis (from /socratic endpoint)
    steps_covered JSONB DEFAULT '[]', -- Which sub-steps student addressed
    current_step_status TEXT DEFAULT 'in_progress' CHECK (current_step_status IN ('not_started', 'in_progress', 'completed', 'needs_review')),
    next_expected_step TEXT,
    ai_feedback TEXT,
    socratic_response TEXT,
    
    -- Scoring
    understanding_score NUMERIC(3,2),
    completeness_score NUMERIC(3,2),
    is_correct BOOLEAN,
    
    -- Timing
    time_spent_seconds INTEGER,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(session_id, step_id, attempt_number)
);

-- =============================================
-- MATERIALS & RAG (For EMBED agent)
-- =============================================

-- Educational materials store
CREATE TABLE public.materials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    material_type TEXT CHECK (material_type IN ('teacher_note', 'lesson', 'example', 'definition', 'video_transcript')),
    
    -- Classification
    standard_codes TEXT[] DEFAULT '{}',
    grade_levels INTEGER[] DEFAULT '{}',
    topics TEXT[] DEFAULT '{}',
    difficulty_level INTEGER,
    
    -- File information
    source_url TEXT,
    file_metadata JSONB DEFAULT '{}',
    
    -- Usage tracking
    retrieval_count INTEGER DEFAULT 0,
    helpfulness_rating NUMERIC(3,2),
    
    uploaded_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vector embeddings for semantic search
CREATE TABLE public.material_embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    material_id UUID NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
    content_chunk TEXT NOT NULL, -- Chunked content for better retrieval
    chunk_index INTEGER DEFAULT 0,
    
    -- Embedding data
    embedding VECTOR(1536), -- OpenAI ada-002 dimensions
    embedding_model TEXT DEFAULT 'text-embedding-ada-002',
    
    -- Metadata for retrieval
    chunk_metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(material_id, chunk_index)
);

-- Create vector similarity index
CREATE INDEX ON material_embeddings USING ivfflat (embedding vector_cosine_ops);

-- =============================================
-- ANALYTICS & INSIGHTS (For /edify-session & teacher dashboards)
-- =============================================

-- Learning analytics summaries
CREATE TABLE public.learning_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Analysis scope
    analysis_type TEXT CHECK (analysis_type IN ('session', 'assignment', 'weekly', 'unit')),
    reference_id UUID, -- Could be session_id, assignment_id, etc.
    time_period_start DATE,
    time_period_end DATE,
    
    -- Key insights (from EDIFY agent)
    strengths JSONB DEFAULT '[]',
    areas_for_improvement JSONB DEFAULT '[]',
    misconceptions_identified JSONB DEFAULT '[]',
    learning_patterns JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    
    -- Quantitative metrics
    engagement_score NUMERIC(3,2) CHECK (engagement_score >= 0 AND engagement_score <= 1),
    understanding_score NUMERIC(3,2) CHECK (understanding_score >= 0 AND understanding_score <= 1),
    persistence_score NUMERIC(3,2) CHECK (persistence_score >= 0 AND persistence_score <= 1),
    growth_indicator TEXT CHECK (growth_indicator IN ('declining', 'stable', 'improving', 'accelerating')),
    
    -- AI metadata
    ai_confidence NUMERIC(3,2),
    ai_model_version TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Profile update log (tracks all AI-suggested profile changes)
CREATE TABLE public.profile_update_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Update source
    source_endpoint TEXT NOT NULL, -- /chat, /socratic, /edify-session
    source_session_id UUID REFERENCES tutoring_sessions(id),
    
    -- Update details
    field_path TEXT NOT NULL, -- e.g., 'mastery_levels.NC.8.EE.7'
    old_value JSONB,
    new_value JSONB NOT NULL,
    update_type TEXT CHECK (update_type IN ('increment', 'decrement', 'set', 'append', 'remove')),
    
    -- Update metadata
    ai_confidence NUMERIC(3,2),
    auto_applied BOOLEAN DEFAULT FALSE,
    applied_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AI MICROSERVICE CACHING & PERFORMANCE
-- =============================================

-- Cache for AI microservice responses
CREATE TABLE public.ai_response_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_key TEXT UNIQUE NOT NULL, -- Hash of request parameters
    endpoint TEXT NOT NULL, -- /chat, /generate-problem, /socratic, /edify-session
    
    -- Request and response data
    request_payload JSONB NOT NULL,
    response_payload JSONB NOT NULL,
    
    -- Cache management
    hit_count INTEGER DEFAULT 0,
    ttl_seconds INTEGER DEFAULT 300,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Performance metrics for AI endpoints
CREATE TABLE public.ai_performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endpoint TEXT NOT NULL,
    
    -- Performance data
    response_time_ms INTEGER NOT NULL,
    token_usage JSONB DEFAULT '{}',
    cache_hit BOOLEAN DEFAULT FALSE,
    
    -- Request context
    user_id UUID REFERENCES users(id),
    session_id UUID REFERENCES tutoring_sessions(id),
    
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Core lookup indexes
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_student_profiles_user_id ON student_profiles(user_id);
CREATE INDEX idx_tutoring_sessions_user_id ON tutoring_sessions(user_id);
CREATE INDEX idx_tutoring_sessions_status ON tutoring_sessions(status);
CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_problem_id ON chat_messages(problem_id);
CREATE INDEX idx_chat_messages_session_problem ON chat_messages(session_id, problem_id);
CREATE INDEX idx_chat_messages_timestamp ON chat_messages(timestamp);
CREATE INDEX idx_student_assignments_user_id ON student_assignments(user_id);
CREATE INDEX idx_student_step_work_session_step ON student_step_work(session_id, step_id);

-- Performance indexes for AI endpoints
CREATE INDEX idx_problems_standard_difficulty ON problems(standard_code, difficulty_level);
CREATE INDEX idx_materials_topics ON materials USING GIN(topics);
CREATE INDEX idx_ai_cache_endpoint ON ai_response_cache(endpoint);
CREATE INDEX idx_ai_cache_expires ON ai_response_cache(expires_at);
CREATE INDEX idx_learning_analytics_user_type ON learning_analytics(user_id, analysis_type);

-- Full-text search indexes
CREATE INDEX idx_materials_content_fts ON materials USING GIN(to_tsvector('english', content));
CREATE INDEX idx_chat_messages_content_fts ON chat_messages USING GIN(to_tsvector('english', content));

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_student_profiles_updated_at BEFORE UPDATE ON student_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_problems_updated_at BEFORE UPDATE ON problems FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assignments_updated_at BEFORE UPDATE ON assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_student_assignments_updated_at BEFORE UPDATE ON student_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tutoring_sessions_updated_at BEFORE UPDATE ON tutoring_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean expired cache entries
CREATE OR REPLACE FUNCTION clean_expired_ai_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM ai_response_cache WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get complete student profile for AI endpoints
CREATE OR REPLACE FUNCTION get_student_profile_for_ai(student_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    profile_data JSONB;
BEGIN
    SELECT jsonb_build_object(
        'user_id', sp.user_id,
        'name', sp.name,
        'grade', sp.grade,
        'learning_preferences', jsonb_build_object(
            'attention_span', sp.attention_span,
            'engagement_level', sp.engagement_level,
            'modality_preference', sp.modality_preference,
            'challenge_preference', sp.challenge_preference,
            'tutoring_style_preference', sp.tutoring_style_preference,
            'optimal_difficulty_level', sp.optimal_difficulty_level
        ),
        'interests', COALESCE(
            (SELECT jsonb_agg(si.interest_name) FROM student_interests si WHERE si.user_id = sp.user_id),
            '[]'::jsonb
        ),
        'explanation_preferences', COALESCE(
            (SELECT jsonb_agg(sep.method_name) FROM student_explanation_preferences sep WHERE sep.user_id = sp.user_id),
            '[]'::jsonb
        ),
        'affective_profile', COALESCE(
            (SELECT jsonb_build_object(
                'interest_level', sap.interest_level,
                'self_efficacy_score', sap.self_efficacy_score,
                'goal_orientation', sap.goal_orientation,
                'mindset', sap.mindset,
                'math_anxiety_level', sap.math_anxiety_level
            ) FROM student_affective_profiles sap WHERE sap.user_id = sp.user_id),
            '{}'::jsonb
        ),
        'mastery_levels', COALESCE(
            (SELECT jsonb_object_agg(sm.standard_code, sm.mastery_level) FROM student_mastery sm WHERE sm.user_id = sp.user_id),
            '{}'::jsonb
        ),
        'misconceptions', COALESCE(
            (SELECT jsonb_agg(jsonb_build_object(
                'concept', smc.concept,
                'description', smc.misconception_description,
                'last_observed', smc.last_observed
            )) FROM student_misconceptions smc WHERE smc.user_id = sp.user_id AND smc.status = 'active'),
            '[]'::jsonb
        )
    ) INTO profile_data
    FROM student_profiles sp
    WHERE sp.user_id = student_user_id;
    
    RETURN profile_data;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on key tables
ALTER TABLE student_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tutoring_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_analytics ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (students can only see their own data)
CREATE POLICY "Students can manage their own profile" ON student_profiles FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Students can view their own sessions" ON tutoring_sessions FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Students can view their own assignments" ON student_assignments FOR ALL USING (user_id = auth.uid());

-- Teachers can view their students' data (implement based on school/class relationships)
-- Public read access for curriculum data
CREATE POLICY "Anyone can view math standards" ON math_standards FOR SELECT USING (true);
CREATE POLICY "Anyone can view problems" ON problems FOR SELECT USING (true);

-- =============================================
-- SAMPLE DATA
-- =============================================

-- Add microservice_payload column to problems table
ALTER TABLE public.problems
ADD COLUMN microservice_payload JSONB DEFAULT '{}';

-- Create chat_responses table for storing /chat endpoint responses
CREATE TABLE public.chat_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES tutoring_sessions(id) ON DELETE CASCADE,
    message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,

    -- Response from /chat endpoint
    reply TEXT NOT NULL,
    moderation_flagged BOOLEAN DEFAULT FALSE,
    moderation_response TEXT,
    agent_outputs JSONB DEFAULT '{}',

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create socratic_responses table for storing /socratic endpoint responses
CREATE TABLE public.socratic_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES tutoring_sessions(id) ON DELETE CASCADE,
    step_work_id UUID REFERENCES student_step_work(id) ON DELETE CASCADE,

    -- Response from /socratic endpoint
    reply TEXT NOT NULL,
    current_step INTEGER,
    steps_covered INTEGER[],
    next_expected INTEGER,
    all_steps_sufficient BOOLEAN DEFAULT FALSE,
    step_scores JSONB DEFAULT '[]',
    agent_outputs JSONB DEFAULT '{}',

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for new tables
CREATE INDEX idx_chat_responses_session_id ON public.chat_responses(session_id);
CREATE INDEX idx_chat_responses_message_id ON public.chat_responses(message_id);
CREATE INDEX idx_socratic_responses_session_id ON public.socratic_responses(session_id);
CREATE INDEX idx_socratic_responses_step_work_id ON public.socratic_responses(step_work_id);
CREATE INDEX idx_problems_microservice_payload ON public.problems USING GIN(microservice_payload);

-- Add triggers for updated_at columns
CREATE TRIGGER update_chat_responses_updated_at BEFORE UPDATE ON chat_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_socratic_responses_updated_at BEFORE UPDATE ON socratic_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert common math standards
INSERT INTO math_standards (code, title, description, grade_level, domain, cluster) VALUES
('8.EE.7', 'Solve linear equations in one variable', 'Give examples of linear equations with one solution, infinitely many solutions, or no solutions', 8, 'Expressions and Equations', 'Analyze and solve linear equations'),
('8.EE.8', 'Analyze and solve pairs of simultaneous linear equations', 'Understand that solutions correspond to points of intersection', 8, 'Expressions and Equations', 'Analyze and solve linear equations'),
('A.REI.3', 'Solve linear equations and inequalities', 'Solve linear equations and inequalities in one variable', 9, 'Algebra', 'Reasoning with Equations');

-- Insert sample problems with microservice payloads for demo
INSERT INTO problems (
    id,
    title,
    statement,
    problem_type,
    standard_code,
    difficulty_level,
    expected_steps,
    correct_answer,
    microservice_payload
) VALUES
(
    uuid_generate_v4(),
    'Droid Delivery Mission',
    'Two objects are moving toward a common point from different starting distances. Object A starts 2 miles away from the point and moves 1 mile every hour. Object B starts 8 miles away and moves 2 miles every hour. Let y represent the distance each object is from the point after x hours. After how many hours will both objects be the same distance from the point?',
    'socratic_conceptual',
    '8.EE.8',
    5,
    '[
        "Write an equation for ObjectA: yA = 2 - 1x",
        "Write an equation for ObjectB: yB = 8 - 2x",
        "Set the distances equal: 2 - 1x = 8 - 2x",
        "Add 2x to both sides: 2 + 1x = 8",
        "Subtract 2 from both sides: x = 6"
    ]'::jsonb,
    '"x = 6"'::jsonb,
    '{
        "statement": "Two objects are moving toward a common point from different starting distances. Object A starts 2 miles away from the point and moves 1 mile every hour. Object B starts 8 miles away and moves 2 miles every hour. Let y represent the distance each object is from the point after x hours. After how many hours will both objects be the same distance from the point?",
        "answer": "x = 6",
        "steps": [
            "Write an equation for ObjectA: yA = 2 - 1x",
            "Write an equation for ObjectB: yB = 8 - 2x",
            "Set the distances equal: 2 - 1x = 8 - 2x",
            "Add 2x to both sides: 2 + 1x = 8",
            "Subtract 2 from both sides: x = 6"
        ]
    }'::jsonb
),
(
    uuid_generate_v4(),
    'Linear Equation Practice',
    'Solve for x: 3x + 5 = 14',
    'mechanical_steps',
    '8.EE.7',
    3,
    '[
        "Start with the equation: 3x + 5 = 14",
        "Subtract 5 from both sides: 3x = 9",
        "Divide both sides by 3: x = 3"
    ]'::jsonb,
    '"x = 3"'::jsonb,
    '{
        "statement": "Solve for x: 3x + 5 = 14",
        "answer": "x = 3",
        "steps": [
            "Start with the equation: 3x + 5 = 14",
            "Subtract 5 from both sides: 3x = 9",
            "Divide both sides by 3: x = 3"
        ]
    }'::jsonb
),
(
    uuid_generate_v4(),
    'System of Equations',
    'Find the intersection point of the lines y = 2x + 1 and y = -x + 7.',
    'socratic_conceptual',
    '8.EE.8',
    6,
    '[
        "Write the system: y = 2x + 1 and y = -x + 7",
        "Set equations equal: 2x + 1 = -x + 7",
        "Add x to both sides: 3x + 1 = 7",
        "Subtract 1 from both sides: 3x = 6",
        "Divide by 3: x = 2",
        "Substitute back: y = 2(2) + 1 = 5"
    ]'::jsonb,
    '"(2, 5)"'::jsonb,
    '{
        "statement": "Find the intersection point of the lines y = 2x + 1 and y = -x + 7.",
        "answer": "(2, 5)",
        "steps": [
            "Write the system: y = 2x + 1 and y = -x + 7",
            "Set equations equal: 2x + 1 = -x + 7",
            "Add x to both sides: 3x + 1 = 7",
            "Subtract 1 from both sides: 3x = 6",
            "Divide by 3: x = 2",
            "Substitute back: y = 2(2) + 1 = 5"
        ]
    }'::jsonb
)
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================

COMMENT ON TABLE student_profiles IS 'Comprehensive student profiles optimized for AI microservice personalization';
COMMENT ON TABLE tutoring_sessions IS 'Individual tutoring sessions that provide context for chat conversations';
COMMENT ON TABLE chat_messages IS 'Complete conversation history with AI agent outputs and analytics';
COMMENT ON TABLE student_step_work IS 'Detailed step-by-step work tracking optimized for /socratic endpoint';
COMMENT ON TABLE material_embeddings IS 'Vector embeddings for RAG functionality (EMBED agent)';
COMMENT ON TABLE ai_response_cache IS 'Performance cache for AI microservice responses';
COMMENT ON TABLE learning_analytics IS 'AI-generated insights and analytics from EDIFY agent';

COMMENT ON FUNCTION get_student_profile_for_ai IS 'Returns complete student profile in format expected by AI microservice endpoints'; 